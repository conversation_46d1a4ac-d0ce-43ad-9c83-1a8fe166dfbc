package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.client.microservice.ServicesGroupClient;
import com.mega.platform.cloud.common.constant.MicroserviceConstants;
import com.mega.platform.cloud.admin.dao.AdminServiceGroupDao;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.client.microservice.ServicesGroupClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import com.mega.platform.cloud.data.entity.ServicesGroupTagRelation;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupRespVO;
import com.mega.platform.cloud.microservice.service.ServicesService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.AdminErrorCode;

import java.math.BigInteger;
import java.util.*;
import com.mega.platform.cloud.common.utils.EnvUtil;

@Service
@Slf4j
public class AdminServiceGroupManageService {
    private final AdminServiceGroupDao adminServiceGroupDao;
    // private final ServicesGroupClient servicesGroupClient;
    private final ServicesService servicesService; 

    public AdminServiceGroupManageService(AdminServiceGroupDao adminServiceGroupDao,
                                          ServicesService servicesService) {
        this.adminServiceGroupDao = adminServiceGroupDao;
        // this.servicesGroupClient = servicesGroupClient;
        this.servicesService = servicesService;
    }

    /**
     * 创建服务组
     * @param projectId 项目ID
     * @param reqVO 创建请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void createServicesGroup(Long projectId, AdminServicesGroupCreateReqVO reqVO) {

        // 检查服务组名称是否重复
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupByProjectIdAndName(
                projectId, reqVO.getServicesGroupName(), null);
        if (existGroup != null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组名称已存在");
        }

        // 构建远程调用参数
        CreateServicesGroupReqVO clientReqVO = buildCreateServicesGroupReqVO(projectId, reqVO);

        try {
            // 调用远程服务创建服务组
            CreateServicesGroupRespVO result = servicesService.createServicesGroup(clientReqVO);
            if (result == null || result.getServicesGroupId() == null) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "创建服务组失败：");
            }

            Long servicesGroupId = result.getServicesGroupId();
            log.info("远程创建服务组成功，servicesGroupId: {}", servicesGroupId);

            // 更新本地扩展字段
            updateLocalServicesGroupFields(servicesGroupId, reqVO);

            // 插入services_group_tag_relation表
            List<ServicesGroupTagRelation> tagRelations = new ArrayList<>();
            for (BigInteger tagId : reqVO.getTags()) {
                ServicesGroupTagRelation tagRelation = new ServicesGroupTagRelation();
                tagRelation.setServiceGroupId(servicesGroupId);
                tagRelation.setServiceTagId(tagId.longValue());
                tagRelation.setCreateTime(new Date());
                tagRelation.setUpdateTime(new Date());
                tagRelation.setDelsign((byte) 0);
                tagRelations.add(tagRelation);
            }
            adminServiceGroupDao.insertServicesGroupTagRelation(tagRelations);

        } catch (Exception e) {
            log.error("创建服务组失败，projectId: {}, servicesGroupName: {}", projectId, reqVO.getServicesGroupName(), e);
            throw  new AdminException(AdminErrorCode.ERR_0.getCode(), "创建服务组失败：" + e.getMessage());
        }
    }

    /**
     * 编辑服务组
     * @param projectId 项目ID
     * @param reqVO 编辑请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editServicesGroup(Long projectId, AdminServicesGroupEditReqVO reqVO) {
        // 1 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组不存在或已删除");
        }
        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "无权限操作该服务组");
        }
        // 检查服务组名称是否重复
        if(StringUtils.hasText(reqVO.getServicesGroupName())) {
            ServicesGroup nameGroup = adminServiceGroupDao.selectServicesGroupByProjectIdAndName(projectId, reqVO.getServicesGroupName(), reqVO.getServicesGroupId());
            if (nameGroup != null) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组名称已存在");
            }
        }
        // 检测 Jenkins模板参数是否符合要求
        Map<String, String> templateParams = jenkinsParamsToMap(reqVO.getJenkinsParams());
        List<JenkinsTemplateParamDTO> templateParamsList = servicesService.checkTemplateParamKey(existGroup.getJenkinsTemplateId(),  MicroserviceConstants.SERVICES_DATA_TYPE_SERVICES_GROUP, templateParams);
 

        // 2 检查是否有运行中的服务，限制可编辑字段
        Integer runningServicesCount = adminServiceGroupDao.countRunningServicesByServicesGroupId(reqVO.getServicesGroupId());
        Integer totalServicesCount = adminServiceGroupDao.countServicesByServicesGroupId(reqVO.getServicesGroupId());
        if(runningServicesCount < 1){
            // 如果没有运行中的服务，允许编辑所有字段, 更新jenkins参数
            this.updateJenkinsTemplateParams(existGroup.getId(), templateParamsList);
        }
    
        // 构建更新对象
        ServicesGroup updateGroup = buildUpdateServicesGroup(reqVO, runningServicesCount > 0, totalServicesCount > 0);
        // 执行更新
        adminServiceGroupDao.updateServicesGroupSelective(updateGroup);

        // 3 处理标签关系变更
        if(reqVO.getTags() != null && reqVO.getTags().size() > 0) {
            List<ServicesGroupTagRelation> updateTagRelations = new ArrayList<>();
            List<ServicesGroupTagRelation> existingTags = adminServiceGroupDao.selectServicesGroupTagRelation(reqVO.getServicesGroupId());
            // 将新标签和要删除的标签添加到更新列表
            for (BigInteger tagId : reqVO.getTags()) {
                // 如果新标签不在现有标签中，则添加到更新列表
                if (existingTags.stream().noneMatch(tag -> tag.getServiceTagId().equals(tagId.longValue()))) {
                    updateTagRelations.add(new ServicesGroupTagRelation()
                                                .setServiceGroupId(reqVO.getServicesGroupId())
                                                .setServiceTagId(tagId.longValue())
                                                .setCreateTime(new Date())
                                                .setUpdateTime(new Date())
                                                .setDelsign((byte) 0)
                                        );
                } 
            }

            // 将要删除的标签添加到更新列表
            existingTags.stream()
                .filter(tag -> reqVO.getTags().stream().noneMatch(newTag -> tag.getServiceTagId().equals(newTag.longValue())))
                .forEach(tag -> {
                    tag.setDelsign((byte) 1);
                    tag.setUpdateTime(new Date());
                    updateTagRelations.add(tag);
                });

            // 执行更新
            adminServiceGroupDao.insertServicesGroupTagRelation(updateTagRelations);
        }
       

        log.info("编辑服务组成功，servicesGroupId: {}, servicesGroupName: {}", reqVO.getServicesGroupId(), reqVO.getServicesGroupName());
    }

    private void updateJenkinsTemplateParams(Long servicesGroupId, List<JenkinsTemplateParamDTO> jenkinsParams) {
        // 更新Jenkins模板参数
        adminServiceGroupDao.updateJenkinsTemplateParams(servicesGroupId, jenkinsParams);
    }

    /**
     * 更改服务组状态
     * @param projectId 项目ID
     * @param reqVO 状态编辑请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editServicesGroupStatus(Long projectId, AdminServicesGroupStatusEditReqVO reqVO) {

        // 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组不存在或已删除");
        }

        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException(AdminErrorCode.ERR_0.getCode(), "无权限操作该服务组");
        }

        // 下线检查：有正在运行的服务不让下线
        if (reqVO.getStatus() == 0) {
            Integer runningServicesCount = adminServiceGroupDao.countRunningServicesByServicesGroupId(reqVO.getServicesGroupId());
            if (runningServicesCount > 0) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组下有正在运行的服务，请先停止所有服务后再下线");
            }
        }

        // 上线检查：是否有服务
        if (reqVO.getStatus() == 1) {
            Integer totalServicesCount = adminServiceGroupDao.countServicesByServicesGroupId(reqVO.getServicesGroupId());
            if (totalServicesCount == 0) {
                throw new AdminException(AdminErrorCode.ERR_0.getCode(), "服务组下没有服务，无法上线");
            }
        }

        // 更新状态
        int result = adminServiceGroupDao.updateServicesGroupStatus(reqVO.getServicesGroupId(), reqVO.getStatus());
        if (result <= 0) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"更新服务组状态失败");
        }

        log.info("更新服务组状态成功，servicesGroupId: {}, status: {}", reqVO.getServicesGroupId(), reqVO.getStatus());
    }

    /**
     * 重启服务组
     * @param projectId 项目ID
     * @param reqVO 操作请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void restartServicesGroup(Long projectId, AdminServicesGroupActionReqVO reqVO) {
        executeServicesGroupAction(projectId, reqVO, 1, "重启");
    }

    /**
     * 停止服务组
     * @param projectId 项目ID
     * @param reqVO 操作请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void stopServicesGroup(Long projectId, AdminServicesGroupActionReqVO reqVO) {
        executeServicesGroupAction(projectId, reqVO, 2, "停止");
    }

    /**
     * 删除服务组
     * @param projectId 项目ID
     * @param reqVO 操作请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteServicesGroup(Long projectId, AdminServicesGroupActionReqVO reqVO) {

        // 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"服务组不存在或已删除");
        }

        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"无权限操作该服务组");
        }

        // 执行软删除
        adminServiceGroupDao.deleteServicesGroupById(reqVO.getServicesGroupId());
        log.info("删除服务组成功，servicesGroupId: {}, servicesGroupName: {}", reqVO.getServicesGroupId(), existGroup.getName());
    }

    // ==================== 私有方法 ====================

    /**
     * 执行服务组操作（重启/停止）
     */
    private void executeServicesGroupAction(Long projectId, AdminServicesGroupActionReqVO reqVO, Integer action, String actionName) {   
        // 检查服务组是否存在
        ServicesGroup existGroup = adminServiceGroupDao.selectServicesGroupById(reqVO.getServicesGroupId());
        if (existGroup == null) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"服务组不存在或已删除");
        }

        // 检查项目权限
        if (!projectId.equals(existGroup.getProjectId())) {
            throw new AdminException( AdminErrorCode.ERR_0.getCode(),"无权限操作该服务组");
        }

        // 构建远程调用参数
        BuildServicesDTO clientReqVO = new BuildServicesDTO();
        clientReqVO.setServicesGroupId(reqVO.getServicesGroupId());
        clientReqVO.setAction(action);
        try {
            // 调用远程服务
            servicesService.buildServicesGroup(clientReqVO);
            log.info("{}服务组成功，servicesroupId: {}", actionName, reqVO.getServicesGroupId());
        } catch (Exception e) {
            log.error("{}服务组失败，servicesGroupId: {}", actionName, reqVO.getServicesGroupId(), e);
            throw new AdminException(AdminErrorCode.ERR_0.getCode(),actionName + "服务组失败：" + e.getMessage());
        }
    }



    /**
     * 构建远程调用参数
     */
    private CreateServicesGroupReqVO buildCreateServicesGroupReqVO(Long projectId, AdminServicesGroupCreateReqVO reqVO) {
        CreateServicesGroupReqVO clientReqVO = new CreateServicesGroupReqVO();

        // 复制基本属性（除了jenkinsParams）
        BeanUtils.copyProperties(reqVO, clientReqVO, "jenkinsParams");

        // 设置项目相关信息
        clientReqVO.setProjectId(projectId);
        // 设计环境
        clientReqVO.setServiceEnv(EnvUtil.getEnv());

        // 处理jenkinsParams JSON字符串
        clientReqVO.setJenkinsParams(jenkinsParamsToMap(reqVO.getJenkinsParams()));
        return clientReqVO;
    }

    /**
     * 将jenkinsParams JSON字符串转换为Map
     * @param jenkinsParams JSON字符串
     * @return Map<String, String>
     */
    private Map<String, String> jenkinsParamsToMap(String jenkinsParams) {
        if (!StringUtils.hasText(jenkinsParams)) {
            return new HashMap<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jenkinsParams, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.warn("解析jenkinsParams JSON失败，使用空Map: {}", jenkinsParams, e);
            return new HashMap<>();
        }
    }

    /**
     * 更新本地扩展字段
     */
    private void updateLocalServicesGroupFields(Long servicesGroupId, AdminServicesGroupCreateReqVO reqVO) {
        // 查询刚创建的服务组
        ServicesGroup servicesGroup = adminServiceGroupDao.selectServicesGroupById(servicesGroupId);
        if (servicesGroup == null) {
            log.warn("创建服务组后查询不到记录，servicesGroupId: {}", servicesGroupId);
            return;
        }

        // 更新扩展字段（在CreateServicesGroupReqVO中没有但在AdminServicesGroupCreateReqVO中有的字段）
        ServicesGroup updateGroup = new ServicesGroup();
        updateGroup.setId(servicesGroupId);
        updateGroup.setRemark(reqVO.getRemark());
        updateGroup.setIsSelf(reqVO.getIsSelf());
        updateGroup.setUseJenkins(reqVO.getUseJenkins().byteValue());
        // 执行更新
        adminServiceGroupDao.updateServicesGroupSelective(updateGroup);
        log.info("更新服务组扩展字段成功，servicesGroupId: {}", servicesGroupId);
    }

    /**
     * 构建编辑时的更新对象
     */
    private ServicesGroup buildUpdateServicesGroup(AdminServicesGroupEditReqVO reqVO, boolean hasRunningServices, boolean hasServices) {
        ServicesGroup updateGroup = new ServicesGroup();
        updateGroup.setId(reqVO.getServicesGroupId());

        if (!hasServices) {
            // 无服务时，所有字段都可改
            updateGroup.setName(reqVO.getServicesGroupName());
            updateGroup.setServicesUpdateType(reqVO.getServiceUpdateId());
            updateGroup.setServicesEnv(reqVO.getServiceEnv());
            updateGroup.setServicesAliveNum(reqVO.getServiceAliveNum());
            updateGroup.setCheckAliveType(reqVO.getCheckAliveType());
            updateGroup.setJenkinsServicesId(reqVO.getJenkinsServiceId());
            updateGroup.setJenkinsTemplateId(reqVO.getJenkinsTemplateId());
            updateGroup.setRemark(reqVO.getRemark());
            updateGroup.setIsSelf(reqVO.getIsSelf());
            updateGroup.setUseJenkins(reqVO.getUseJenkins().byteValue());
            updateGroup.setServicesLogFormatId(reqVO.getServicesLogFormatId());
            updateGroup.setAdminUserId(reqVO.getAdminUserId());
        } else {
            // 有服务时，只能改部分字段：备注、保活数量、管理员、is_self、services_log_format_id
            updateGroup.setRemark(reqVO.getRemark());
            updateGroup.setServicesAliveNum(reqVO.getServiceAliveNum());
            updateGroup.setAdminUserId(reqVO.getAdminUserId());
            updateGroup.setIsSelf(reqVO.getIsSelf());
            updateGroup.setServicesLogFormatId(reqVO.getServicesLogFormatId());
        }

        updateGroup.setUpdateTime(new Date());
        return updateGroup;
    }

    public List<AdminServicesGroupRespVO> listServicesGroups(Long projectId, AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRespVO> respVOS = adminServiceGroupDao.selectServicesGroups(projectId, reqVO);
        return respVOS;
    }
}


