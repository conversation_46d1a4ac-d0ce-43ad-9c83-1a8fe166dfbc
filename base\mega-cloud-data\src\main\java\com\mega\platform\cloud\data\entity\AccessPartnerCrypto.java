package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "access_partner_crypto")
public class AccessPartnerCrypto {
    /**
     * 项目应用ID
     */
    @Id
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 服务名称，如gossipharbor-cloud-exchange
     */
    @Id
    @Column(name = "server_name")
    private String serverName;

    /**
     * 加密密钥
     */
    @Column(name = "crypto_key")
    private String cryptoKey;

    /**
     * 存储MD5信息的JSON
     */
    @Column(name = "md5_json")
    private String md5Json;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标记：0-未删除 1-已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}
