package com.mega.platform.cloud.microservice.service;

import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.common.mapper.JenkinsTaskLogMapper;
import com.mega.platform.cloud.common.mapper.ServicesGroupMapper;
import com.mega.platform.cloud.common.mapper.ServicesMapper;
import com.mega.platform.cloud.common.utils.RedisUtils;
import com.mega.platform.cloud.core.utils.DateUtils;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTaskDTO;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.entity.JenkinsTaskLog;
import com.mega.platform.cloud.data.entity.Services;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import com.mega.platform.cloud.microservice.dao.JenkinsDao;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MicroserviceCommonService {
    private final JenkinsTaskLogMapper jenkinsTaskLogMapper;
    private final ServicesMapper servicesMapper;
    private final ServicesGroupMapper servicesGroupMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final Environment environment;
    private final JenkinsDao jenkinsDao;

    @Autowired
    public MicroserviceCommonService(JenkinsTaskLogMapper jenkinsTaskLogMapper, ServicesMapper servicesMapper, ServicesGroupMapper servicesGroupMapper, RedisUtils redisUtils, StringRedisTemplate stringRedisTemplate, Environment environment, JenkinsDao jenkinsDao) {
        this.jenkinsTaskLogMapper = jenkinsTaskLogMapper;
        this.servicesMapper = servicesMapper;
        this.servicesGroupMapper = servicesGroupMapper;
        this.stringRedisTemplate = stringRedisTemplate;
        this.environment = environment;
        this.jenkinsDao = jenkinsDao;
    }

    private final static String LOG_TASK_FORMAT = "[%s][%s/%s][%s-%s][%s][%s] - %s";
    private final static String LOG_TASK_GROUP_FORMAT = "[%s][%s][%s] - %s";

    /**
     * 记录任务构建时log
     *
     * @param taskDTO
     * @param contentFormat
     * @param args
     */
    public void logTaskContent(JenkinsTaskDTO taskDTO, String contentFormat, Object... args) {
        String content = String.format(contentFormat, args);
        String dateStr = DateUtils.formatTime(new Date());
        String log = String.format(LOG_TASK_FORMAT, dateStr, taskDTO.getIndex(), taskDTO.getTaskGroupTaskNum(), taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), taskDTO.getServicesName(), ServiceGroupBuildActionEnum.findByAction(taskDTO.getRealAction()).getShowStr(), content);
        jenkinsTaskLogMapper.insertSelective(new JenkinsTaskLog().setJenkinsTaskGroupId(taskDTO.getJenkinsTaskGroupId()).setJenkinsTaskId(taskDTO.getJenkinsTaskId()).setLogContent(log));
    }

    /**
     * 记录任务构建时log
     *
     * @param taskDTO
     * @param contentFormat
     * @param args
     */
    public void logTaskGroupContent(JenkinsTaskDTO taskDTO, String contentFormat, Object... args) {
        String content = String.format(contentFormat, args);
        String dateStr = DateUtils.formatTime(new Date());
        String log = String.format(LOG_TASK_GROUP_FORMAT, dateStr, taskDTO.getJenkinsTaskGroupId(), taskDTO.getServicesGroupName(), content);
        jenkinsTaskLogMapper.insertSelective(new JenkinsTaskLog().setJenkinsTaskGroupId(taskDTO.getJenkinsTaskGroupId()).setLogContent(log));
    }

    /**
     * 记录任务构建时log
     *
     * @param jenkinsTaskGroupId
     * @param servicesGroupName
     * @param contentFormat
     * @param args
     */
    public void logTaskGroupContent(Long jenkinsTaskGroupId, String servicesGroupName, String contentFormat, Object... args) {
        String content = String.format(contentFormat, args);
        String dateStr = DateUtils.formatTime(new Date());
        String log = String.format(LOG_TASK_GROUP_FORMAT, dateStr, jenkinsTaskGroupId, servicesGroupName, content);
        jenkinsTaskLogMapper.insertSelective(new JenkinsTaskLog().setJenkinsTaskGroupId(jenkinsTaskGroupId).setLogContent(log));
    }

    /**
     * 更新服务运行状态
     *
     * @param servicesId
     * @param status
     */
    public void updateServicesRunningStatus(Long servicesId, Integer status) {
        servicesMapper.updateByPrimaryKeySelective(new Services().setId(servicesId).setRunningStatus(status));
    }

    /**
     * 更新服务组运行状态
     *
     * @param servicesGroupId
     * @param status
     */
    public void updateServicesGroupRunningStatus(Long servicesGroupId, Integer status) {
        servicesGroupMapper.updateByPrimaryKeySelective(new ServicesGroup().setId(servicesGroupId).setRunningStatus(status));
    }

    /**
     * 构建上锁
     * @param servicesGroupId
     * @return
     */
    public Boolean buildTryLock(Long servicesGroupId) {
        Boolean tryLock = stringRedisTemplate.opsForValue().setIfAbsent(String.format("mega-cloud-microservice:servicesGroupBuildLock:%s", servicesGroupId), serviceIpPort());
        if (tryLock) {
            log.info("buildTryLock servicesGroupId: {} success", servicesGroupId);
            return true;
        } else {
            tryLock = stringRedisTemplate.opsForValue().get(String.format("mega-cloud-microservice:servicesGroupBuildLock:%s", servicesGroupId)).equals(serviceIpPort());
            log.info("buildTryLock servicesGroupId: {} exists tryLock: {}", servicesGroupId, tryLock);
            return tryLock;
        }
    }

    /**
     * 构建释放锁
     * @param servicesGroupId
     */
    public void buildReleaseLock(Long servicesGroupId) {
        log.info("buildReleaseLock servicesGroupId: {}", servicesGroupId);
        stringRedisTemplate.delete(String.format("mega-cloud-microservice:servicesGroupBuildLock:%s", servicesGroupId));
    }

    @SneakyThrows
    public String serviceIpPort() {
        return String.format("%s:%s", InetAddress.getLocalHost().getHostAddress(), environment.getProperty("server.port"));
    }

    /**
     * 获取某个services或servicesGroup指定param的值
     *
     * @param servicesDataId
     * @param servicesDataType
     * @param param
     * @return
     */
    public String getServicesDataJenkinsParamValue(Long servicesDataId, Integer servicesDataType, String param) {
        return jenkinsDao.getServicesDataJenkinsParamValue(servicesDataId, servicesDataType, param);
    }

    /**
     * 获取某个services或servicesGroup所有param的值
     *
     * @param servicesDataId
     * @param servicesDataType
     * @return
     */
    public Map<String, String> getServicesDataJenkinsParamValues(Long servicesDataId, Integer servicesDataType) {
        Map<String, String> dataMap = new HashMap<>();
        List<JenkinsTemplateParamDTO> paramDTOS = jenkinsDao.getServicesDataJenkinsParamValues(servicesDataId, servicesDataType);
        for (JenkinsTemplateParamDTO paramDTO : paramDTOS) {
            dataMap.put(paramDTO.getParamKey(), paramDTO.getParamValue());
        }
        return dataMap;
    }
}
