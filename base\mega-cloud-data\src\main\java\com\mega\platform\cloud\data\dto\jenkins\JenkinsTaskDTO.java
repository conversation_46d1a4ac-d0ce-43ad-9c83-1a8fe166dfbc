package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class JenkinsTaskDTO {
    public String serverIp;
    public Integer servicesPort;
    public Boolean serviceIsRunning;
    public Integer taskGroupAction;
    public Integer realAction;
    public Integer servicesUpdateType;
    public Long jenkinsJobId;
    public Long servicesId;
    public Long servicesGroupId;
    public Long jenkinsTaskId;
    public Long jenkinsTaskGroupId;
    public String jenkinsJobName;
    public String servicesName;
    public String servicesGroupName;
    public Map<String, String> jenkinsTaskParams;
    public Integer index;
    public Integer taskGroupTaskNum;
    public Integer checkAliveType;

}
