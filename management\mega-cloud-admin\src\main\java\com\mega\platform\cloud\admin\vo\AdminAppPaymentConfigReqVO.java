package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("应用支付配置")
public class AdminAppPaymentConfigReqVO {
    @ApiModelProperty("应用ID")
    private Long projectAppId;

    @ApiModelProperty("第三方通用平台配置")
    private Long thirdPlatformId;

    @ApiModelProperty("阿里支付配置")
    private PaymentAlipayConfig paymentAlipayConfig;

    @ApiModelProperty("微信支付配置")
    private PaymentWechatConfig paymentWechatConfig;

    @ApiModelProperty("苹果支付配置")
    private PaymentAppleConfig paymentAppleConfig;
}
