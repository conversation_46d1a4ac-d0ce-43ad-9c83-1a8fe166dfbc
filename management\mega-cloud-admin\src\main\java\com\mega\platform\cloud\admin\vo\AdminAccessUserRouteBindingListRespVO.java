package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-管理员路由绑定列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员路由绑定列表响应")
public class AdminAccessUserRouteBindingListRespVO {
    @ApiModelProperty("管理员ID")
    private Long adminUserId;


    @ApiModelProperty("路由ID")
    private Long adminRouterId;


    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}