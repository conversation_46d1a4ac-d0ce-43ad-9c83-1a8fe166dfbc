package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.dto.AdminUrlPatternDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FeignClient扫描数据访问层
 */
@Mapper
public interface AdminScannerDao {
    
    /**
     * 批量插入或更新URL模式
     * 使用 INSERT ... ON DUPLICATE KEY UPDATE 语法
     * 当url_pattern冲突时更新name字段
     * 
     * @param urlPatternList URL模式列表
     * @return 影响的行数
     */
    int batchInsertOrUpdateUrlPatterns(@Param("urlPatternList") List<AdminUrlPatternDTO> urlPatternList);
}