package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理-管理员与项目更新绑定请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员与项目更新绑定请求")
public class AdminAccessUserProjectBindingUpdateReqVO {

    @ApiModelProperty("管理员ID")
    @NotNull(message = "管理员ID不能为空")
    private Long userId;

    @ApiModelProperty("项目ID")
    @NotNull(message = "项目ID")
    private Long projectId;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;
}