package com.mega.platform.cloud.monitor.dao;

import com.mega.platform.cloud.data.dto.monitor.ServicesGroupRunningStatusScanDTO;
import com.mega.platform.cloud.data.dto.monitor.ServicesRunningStatusScanDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServicesDao {
    List<ServicesRunningStatusScanDTO> getAllOnlineServices();

    String getServiceParam(@Param("servicesId") Long servicesId, @Param("key") String key);

    List<ServicesGroupRunningStatusScanDTO> getAllOnlineServicesGroup();
}