package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "access_partner_license_active_record")
public class AccessPartnerLicenseActiveRecord {
    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 项目应用ID
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 内网IP地址
     */
    @Column(name = "inner_ip")
    private String innerIp;

    /**
     * 公网IP地址
     */
    @Column(name = "public_ip")
    private String publicIp;

    /**
     * 机器唯一标识
     */
    @Column(name = "machine_id")
    private String machineId;

    /**
     * License授权码
     */
    @Column(name = "license")
    private String license;

    /**
     * 激活结果：0-失败 1-成功
     */
    @Column(name = "success")
    private Byte success;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 服务名称，如gossipharbor-cloud-exchange
     */
    @Column(name = "server_name")
    private String serverName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
