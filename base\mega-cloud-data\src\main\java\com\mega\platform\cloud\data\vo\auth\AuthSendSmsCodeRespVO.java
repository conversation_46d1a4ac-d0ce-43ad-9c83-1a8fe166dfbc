package com.mega.platform.cloud.data.vo.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("发送短信验证码响应")
public class AuthSendSmsCodeRespVO {
    @ApiModelProperty(value = "验证码有效时间（秒）", example = "600")
    private int expireSecond;

    @ApiModelProperty(value = "验证码", example = "123456", notes = "仅测试/开发环境返回")
    private String code;
}
