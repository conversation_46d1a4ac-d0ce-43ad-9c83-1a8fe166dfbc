package com.mega.platform.cloud.data.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_order")
public class PaymentOrder {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 商品ID
     */
    @Column(name = "payment_product_config_id")
    private Long paymentProductConfigId;

    /**
     * 签约类父订单ID（本表）
     */
    @Column(name = "parent_payment_order_id")
    private Long parentPaymentOrderId;

    /**
     * 支付平台订单号，比如alipay
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 外显应用订单号（中台算法随机生成）
     */
    @Column(name = "out_order_no")
    private String outOrderNo;

    /**
     * 订单总金额
     */
    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 币种
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 订单状态（0=初始化, 1=成功, 2=失败, 3=已退款）
     */
    @Column(name = "status")
    private Byte status;

    /**
     * 是否为沙盒订单（1=是，0=否）
     */
    @Column(name = "sand_box")
    private Short sandBox;

    /**
     * 支付完成时间
     */
    @Column(name = "pay_time")
    private Date payTime;

    /**
     * 扩展信息（JSON）
     */
    @Column(name = "extra")
    private String extra;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}