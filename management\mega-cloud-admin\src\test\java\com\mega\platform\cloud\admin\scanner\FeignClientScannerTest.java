package com.mega.platform.cloud.admin.scanner;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;

import java.util.Set;

/**
 * FeignClient扫描功能测试
 */
public class FeignClientScannerTest {

    public static void main(String[] args) {
        new FeignClientScannerTest().testFeignClientScanning();
    }

    public void testFeignClientScanning() {
        System.out.println("开始测试FeignClient扫描功能...");
        
        // 使用ClassPathScanningCandidateComponentProvider扫描FeignClient接口
        // 重写isCandidateComponent方法以支持接口扫描
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false) {
            @Override
            protected boolean isCandidateComponent(org.springframework.beans.factory.annotation.AnnotatedBeanDefinition beanDefinition) {
                // 允许扫描接口
                return beanDefinition.getMetadata().isInterface() || super.isCandidateComponent(beanDefinition);
            }
        };
        scanner.addIncludeFilter(new AnnotationTypeFilter(FeignClient.class));
        
        // 测试不同的包路径
        String[] testPackages = {
            "com.mega.platform.cloud.client",
            "com.mega.platform.cloud.client.*",
            "com.mega.platform.cloud.client.access",
            "com.mega.platform.cloud.client.auth",
            "com.mega.platform.cloud.client.payment"
        };
        
        for (String basePackage : testPackages) {
            System.out.println("\n测试包路径: " + basePackage);
            try {
                Set<BeanDefinition> candidateComponents = scanner.findCandidateComponents(basePackage);
                System.out.println("找到候选组件数量: " + candidateComponents.size());
                
                for (BeanDefinition beanDefinition : candidateComponents) {
                    String className = beanDefinition.getBeanClassName();
                    System.out.println("  - 找到类: " + className);
                    
                    try {
                        Class<?> clazz = Class.forName(className);
                        if (clazz.isInterface() && clazz.isAnnotationPresent(FeignClient.class)) {
                            FeignClient feignClient = clazz.getAnnotation(FeignClient.class);
                            System.out.println("    FeignClient value: " + feignClient.value());
                            System.out.println("    FeignClient contextId: " + feignClient.contextId());
                        }
                    } catch (ClassNotFoundException e) {
                        System.err.println("    无法加载类: " + className + ", 错误: " + e.getMessage());
                    }
                }
            } catch (Exception e) {
                System.err.println("扫描包 " + basePackage + " 时出错: " + e.getMessage());
            }
        }
        
        System.out.println("\nFeignClient扫描测试完成");
    }
}