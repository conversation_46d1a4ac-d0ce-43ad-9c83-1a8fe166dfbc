package com.mega.platform.cloud.data.dto.common.feishu;

import com.mega.platform.cloud.data.annotation.FeishuParam;
import com.mega.platform.cloud.data.dto.monitor.ServicesRunningStatusScanDTO;
import lombok.Data;

@Data
public class MonitorAlarmServicesRunningStatusFeishuDTO extends BaseFeishuDTO {
    public MonitorAlarmServicesRunningStatusFeishuDTO(ServicesRunningStatusScanDTO dto) {
        super.setTitle("服务运行状态错误");
        this.projectName = dto.getProjectName();
        this.servicesGroupName = dto.getServicesGroupName();
        this.servicesName = dto.getName();
        this.runningStatusStr = getRunningStatusStr(dto.getRunningStatus());
        this.realRunningStatusStr = getRunningStatusStr(dto.getRealRunningStatus());
        super.addTag("监控");
    }

    @FeishuParam("项目名")
    private String projectName;
    @FeishuParam("服务组名")
    private String servicesGroupName;
    @FeishuParam("服务名")
    private String servicesName;
    @FeishuParam("期望运行状态")
    private String runningStatusStr;
    @FeishuParam("真实运行状态")
    private String realRunningStatusStr;

    private String getRunningStatusStr(Integer runningStatus) {
        if (runningStatus == 1) {
            return "运行中";
        }
        if (runningStatus == 0) {
            return "未运行";
        }
        if (runningStatus == 3) {
            return "部分运行";
        }
        return "未知";
    }
}
