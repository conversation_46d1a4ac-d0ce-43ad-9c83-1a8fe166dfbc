package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.service.AdminServiceGroupManageService;
import com.mega.platform.cloud.admin.service.AdminServiceManageService;
import com.mega.platform.cloud.admin.service.AdminServiceQueryService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/admin/api")
@Api(tags = "service管理")
@Slf4j
@RequiredArgsConstructor
@Validated
public class AdminServiceManageController {
    private final AdminServiceManageService adminServiceManageService;
    private final AdminServiceGroupManageService adminServiceGroupManageService;
    private final AdminServiceQueryService adminServiceQueryService;

    /**
     * 创建服务组
     */
    @ApiOperation("创建服务组")
    @PostMapping("/{projectId}/microservice/service-group/create")
    public Result<?> createServicesGroup(@PathVariable("projectId") Long projectId,
                                        @Valid @RequestBody AdminServicesGroupCreateReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.createServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 编辑服务组
     */
    @ApiOperation("编辑服务组")
    @PostMapping("/{projectId}/microservice/service-group/edit")
    public Result<?> editServicesGroup(@PathVariable("projectId") Long projectId,
                                      @Valid @RequestBody AdminServicesGroupEditReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.editServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 更改服务组状态
     */
    @ApiOperation("更改服务组状态")
    @PostMapping("/{projectId}/microservice/service-group/status/edit")
    public Result<?> editServicesGroupStatus(@PathVariable("projectId") Long projectId,
                                            @Valid @RequestBody AdminServicesGroupStatusEditReqVO reqVO) {
        adminServiceGroupManageService.editServicesGroupStatus(projectId, reqVO);
        return Results.success();
    }

    /**
     * 重启服务组
     */
    @ApiOperation("重启服务组")
    @PostMapping("/{projectId}/microservice/service-group/restart")
    public Result<?> restartServicesGroup(@PathVariable("projectId") Long projectId,
                                         @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.restartServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 停止服务组
     */
    @ApiOperation("停止服务组")
    @PostMapping("/{projectId}/microservice/service-group/stop")
    public Result<?> stopServicesGroup(@PathVariable("projectId") Long projectId,
                                      @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.stopServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 删除服务组
     */
    @ApiOperation("删除服务组")
    @PostMapping("/{projectId}/microservice/service-group/delete")
    public Result<?> deleteServicesGroup(@PathVariable("projectId") Long projectId,
                                        @Valid @RequestBody AdminServicesGroupActionReqVO reqVO, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
        reqVO.setAdminUserId(adminUserId);
        adminServiceGroupManageService.deleteServicesGroup(projectId, reqVO);
        return Results.success();
    }

    /**
     * 服务组列表
     */
    @ApiOperation("服务组列表")
    @PostMapping({"/{projectId}/microservice/service/group/list", "/system/microservice/service/group/list"})
    public Result<List<AdminServicesGroupRespVO>> listServiceGroups(
            @PathVariable(value = "projectId", required = false) Long projectId,
            @Validated @RequestBody AdminServicesGroupQueryReqVO reqVO) {
        List<AdminServicesGroupRespVO> groupList = adminServiceGroupManageService.listServicesGroups(projectId, reqVO);
        return Results.success(groupList);
    }


    /**
     * 创建服务
     */
    @ApiOperation("创建服务")
    @PostMapping("/{projectId}/microservice/service/create")
    public Result<?> createService(@Validated @RequestBody AdminServicesCreateReqVO reqVO,
                                   HttpServletRequest request, @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            reqVO.setAdminUserId(adminUserId);
            adminServiceManageService.createService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("创建服务失败，projectId: {}, servicesName: {}", projectId, reqVO.getServicesName(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 编辑服务
     */
    @ApiOperation("编辑服务")
    @PostMapping("/{projectId}/microservice/service/edit")
    public Result<?> editService(@Validated @RequestBody AdminServicesEditReqVO reqVO,
                                 @PathVariable String projectId) {
        try {
            adminServiceManageService.editService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("编辑服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 上线下线服务
     */
    @ApiOperation("上线下线服务")
    @PostMapping("/{projectId}/microservice/service/status/edit")
    public Result<?> editServiceStatus(@Validated @RequestBody AdminServicesStatusEditReqVO reqVO,
                                 @PathVariable String projectId) {
        try {
            adminServiceManageService.editServiceStatus(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("上线下线服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }


    /**
     * 停止服务
     */
    @ApiOperation("停止服务")
    @PostMapping("/{projectId}/microservice/service/status/stop")
    public Result<?> stopService(@Validated @RequestBody AdminServicesHandleReqVO reqVO, HttpServletRequest request,
                                 @PathVariable String projectId) {
        try {
            Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
            adminServiceManageService.stopService(reqVO, adminUserId);
            return Results.success();
        } catch (Exception e) {
            log.error("停止服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 删除服务
     */
    @ApiOperation("删除服务")
    @PostMapping("/{projectId}/microservice/service/status/delete")
    public Result<?> deleteService(@Validated @RequestBody AdminServicesHandleReqVO reqVO, HttpServletRequest request,
                                 @PathVariable String projectId) {
        try {
            adminServiceManageService.deleteService(reqVO);
            return Results.success();
        } catch (Exception e) {
            log.error("删除服务失败，servicesId: {}", reqVO.getServicesId(), e);
            return Results.error(0, e.getMessage(), null);
        }
    }

    /**
     * 项目查询全部services列表
     */
    @PostMapping("/{projectId}/microservice/service/list")
    @ApiOperation(value = "项目查询全部services列表", notes = "支持多种条件筛选查询services列表")
    public Result<List<AdminServicesListRespVO>> getServicesList(@PathVariable("projectId") Long projectId, @RequestBody(required = false) AdminServicesListReqVO reqVO) {
        List<AdminServicesListRespVO> result = adminServiceQueryService.getServicesList(projectId, reqVO);
        return Results.success(result);
    }

    /**
     * 系统查询全部services列表
     */
    @PostMapping("/system/s-microservice/service/list")
    @ApiOperation(value = "系统查询全部services列表", notes = "支持多种条件筛选查询services列表")
    public Result<List<AdminServicesListRespVO>> getServicesListBySystem(@RequestBody(required = false) AdminServicesListReqVO reqVO) {
        List<AdminServicesListRespVO> result = adminServiceQueryService.getServicesList(null, reqVO);
        return Results.success(result);
    }


    /**
     * 基于组查询services列表
     */
    @PostMapping("/{projectId}/microservice/group/service/list")
    @ApiOperation(value = "基于组查询services列表", notes = "查询指定服务组下的所有services")
    public Result<List<AdminServicesListByGroupRespVO>> getServicesListByGroup(@PathVariable("projectId") Long projectId,@RequestBody @Valid AdminServicesListByGroupReqVO reqVO) {
        List<AdminServicesListByGroupRespVO> result = adminServiceQueryService.getServicesListByGroup(projectId, reqVO);
        return Results.success(result);
    }
}

