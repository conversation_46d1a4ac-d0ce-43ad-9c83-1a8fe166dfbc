package com.mega.platform.cloud.microservice.service;

import com.mega.platform.cloud.MicroServiceException;
import com.mega.platform.cloud.common.enums.CheckAliveTypeEnum;
import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.common.mapper.*;
import com.mega.platform.cloud.common.utils.ServicesCheckUtils;
import com.mega.platform.cloud.common.utils.StringUtils;
import com.mega.platform.cloud.data.dto.jenkins.*;
import com.mega.platform.cloud.data.entity.*;
import com.mega.platform.cloud.microservice.dao.JenkinsDao;
import com.mega.platform.cloud.microservice.dao.ServicesDao;
import com.mega.platform.cloud.microservice.service.jenkins.JenkinsApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.mega.platform.cloud.MicroServiceErrorCode.*;
import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_0;
import static com.mega.platform.cloud.common.constant.MicroserviceConstants.*;

@Slf4j
@Service
public class JenkinsService {

    private final JenkinsApiClient jenkinsApiClient;
    private final JenkinsSshServerMapper jenkinsSshServerMapper;
    private final JenkinsViewMapper jenkinsViewMapper;
    private final ProjectMapper projectMapper;
    private final JenkinsDao jenkinsDao;
    private final ServicesMapper servicesMapper;
    private final JenkinsJobTemplateMapper jenkinsJobTemplateMapper;
    private final JenkinsTaskMapper jenkinsTaskMapper;
    private final JenkinsTaskGroupMapper jenkinsTaskGroupMapper;
    private final JenkinsJobMapper jenkinsJobMapper;
    private final RestartService restartService;
    private final ServicesGroupMapper servicesGroupMapper;
    private final MicroserviceCommonService microserviceCommonService;
    private final JenkinsUserMapper jenkinsUserMapper;
    private final ServicesDao servicesDao;

    @Autowired
    public JenkinsService(JenkinsApiClient jenkinsApiClient, JenkinsSshServerMapper jenkinsSshServerMapper, JenkinsViewMapper jenkinsViewMapper, ProjectMapper projectMapper, JenkinsDao jenkinsDao, ServicesMapper servicesMapper, JenkinsJobTemplateMapper jenkinsJobTemplateMapper,
                          JenkinsTaskMapper jenkinsTaskMapper, JenkinsTaskGroupMapper jenkinsTaskGroupMapper, JenkinsJobMapper jenkinsJobMapper, RestartService restartService, ServicesGroupMapper servicesGroupMapper, MicroserviceCommonService microserviceCommonService,
                          JenkinsUserMapper jenkinsUserMapper, ServicesDao servicesDao) {
        this.jenkinsApiClient = jenkinsApiClient;
        this.jenkinsSshServerMapper = jenkinsSshServerMapper;
        this.jenkinsViewMapper = jenkinsViewMapper;
        this.projectMapper = projectMapper;
        this.jenkinsDao = jenkinsDao;
        this.servicesMapper = servicesMapper;
        this.jenkinsJobTemplateMapper = jenkinsJobTemplateMapper;
        this.jenkinsTaskMapper = jenkinsTaskMapper;
        this.jenkinsTaskGroupMapper = jenkinsTaskGroupMapper;
        this.jenkinsJobMapper = jenkinsJobMapper;
        this.restartService = restartService;
        this.servicesGroupMapper = servicesGroupMapper;
        this.microserviceCommonService = microserviceCommonService;
        this.jenkinsUserMapper = jenkinsUserMapper;
        this.servicesDao = servicesDao;
    }

    /**
     * 创建jenkinsUser
     * 不存在向jenkins创建
     *
     * @param jenkinsServices
     * @param adminUser
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public JenkinsUser createJenkinsUser(JenkinsServices jenkinsServices, AdminUser adminUser) throws Exception {
        JenkinsUser jenkinsUser = jenkinsUserMapper.selectOne(new JenkinsUser().setJenkinsServicesId(jenkinsServices.getId()).setAdminUserId(adminUser.getId()));
        if (null == jenkinsUser) {
            JenkinsUser adminJenkinsUser = jenkinsUserMapper.selectByPrimaryKey(1L);
            log.info("createJenkinsSshServer new username:{}", adminUser.getUsername());
            CreateJenkinsUserDTO dto = new CreateJenkinsUserDTO(adminUser.getUsername());
            BaseJenkinsDTO baseDto = new BaseJenkinsDTO(jenkinsServices, adminJenkinsUser);
            jenkinsApiClient.createJenkinsUser(dto, baseDto);
            String jenkinsUserToken = jenkinsApiClient.createJenkinsUserToken(new CreateJenkinsUserTokenDTO(dto.getUserName()), baseDto);
            jenkinsUser = new JenkinsUser();
            jenkinsUser.setJenkinsServicesId(jenkinsServices.getId());
            jenkinsUser.setAdminUserId(adminUser.getId());
            jenkinsUser.setJenkinsUsername(dto.getUserName());
            jenkinsUser.setApiToken(jenkinsUserToken);
            jenkinsDao.insertJenkinsUser(jenkinsUser);
            log.info("createJenkinsUser new username {} success", dto.getUserName());
            return jenkinsUser;
        } else {
            log.info("createJenkinsUser existed username {}", jenkinsUser.getJenkinsUsername());
            return jenkinsUser;
        }
    }


    /**
     * 创建jenkinsSshServer
     * 不存在向jenkins创建
     *
     * @param jenkinsServices
     * @param jenkinsUser
     * @param ecsServer
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public JenkinsSshServer createJenkinsSshServer(JenkinsServices jenkinsServices, JenkinsUser jenkinsUser, EcsServer ecsServer) throws Exception {
        JenkinsSshServer jenkinsSshServer = jenkinsSshServerMapper.selectOne(new JenkinsSshServer().setJenkinsServiceId(jenkinsServices.getId()).setEcsServerId(ecsServer.getId()));
        if (null == jenkinsSshServer) {
            String sshServerName = getJenkinsSshServerName(ecsServer);
            log.info("createJenkinsSshServer new sshServerName:{}", sshServerName);
            CreateJenkinsSshServerDTO dto = new CreateJenkinsSshServerDTO(sshServerName, ecsServer.getPrivateIp()); // 用户名 密码 端口 现在都是默认的
            BaseJenkinsDTO baseDto = new BaseJenkinsDTO(jenkinsServices, jenkinsUser);
            jenkinsApiClient.createJenkinsSshServer(dto, baseDto);
            jenkinsSshServer = new JenkinsSshServer();
            jenkinsSshServer.setServerName(sshServerName);
            jenkinsSshServer.setJenkinsServiceId(jenkinsServices.getId());
            jenkinsSshServer.setEcsServerId(ecsServer.getId());
            jenkinsSshServer.setUsername(dto.getUserName());
            jenkinsSshServer.setPassword(dto.getPassword());
            jenkinsDao.insertJenkinsSshServer(jenkinsSshServer);
            log.info("createJenkinsSshServer new sshServerName {} success", sshServerName);
            return jenkinsSshServer;
        } else {
            log.info("createJenkinsSshServer existed sshServerName {}", jenkinsSshServer.getServerName());
            return jenkinsSshServer;
        }
    }

    /**
     * 创建jenkinsView 不存在向jenkins创建
     *
     * @param jenkinsServices
     * @param jenkinsUser
     * @param servicesGroup
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public JenkinsView createJenkinsView(JenkinsServices jenkinsServices, JenkinsUser jenkinsUser, ServicesGroup servicesGroup) throws Exception {
        JenkinsView jenkinsView = jenkinsViewMapper.selectOne(new JenkinsView().setJenkinsServicesId(jenkinsServices.getId()).setServicesGroupId(servicesGroup.getId()));
        if (null == jenkinsView) {
            String jenkinsViewName = getJenkinsViewName(servicesGroup);
            log.info("createJenkinsView new jenkinsViewName {}", jenkinsViewName);
            BaseJenkinsDTO baseDto = new BaseJenkinsDTO(jenkinsServices, jenkinsUser);
            jenkinsApiClient.createJenkinsView(jenkinsViewName, baseDto);
            jenkinsView = new JenkinsView();
            jenkinsView.setViewName(jenkinsViewName);
            jenkinsView.setProjectId(servicesGroup.getProjectId());
            jenkinsView.setProjectAppId(servicesGroup.getProjectAppId());
            jenkinsView.setJenkinsServicesId(jenkinsServices.getId());
            jenkinsView.setServicesGroupId(servicesGroup.getId());
            jenkinsDao.insertJenkinsView(jenkinsView);
            log.info("createJenkinsView new jenkinsViewName {} success", jenkinsViewName);
            return jenkinsView;
        } else {
            log.info("createJenkinsView existed jenkinsViewName {}", jenkinsView.getViewName());
            return jenkinsView;
        }
    }

    /**
     * 创建jenkinsJob 不存在向jenkins创建
     *
     * @param jenkinsServices
     * @param jenkinsUser
     * @param jenkinsView
     * @param jenkinsSshServer
     * @param servicesGroup
     * @param services
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public JenkinsJob createJenkinsJob(JenkinsServices jenkinsServices, JenkinsUser jenkinsUser, JenkinsView jenkinsView, JenkinsSshServer jenkinsSshServer, ServicesGroup servicesGroup, Services services) throws Exception {
        JenkinsJob jenkinsJob = jenkinsDao.getSameEcsServerJenkinsJob(servicesGroup.getId(), jenkinsSshServer.getEcsServerId(), services.getId());
        if (jenkinsJob != null) {
            // 已存在 直接复用这个jenkinsJob
            log.info("createJenkinsJob existed jenkinsJobName {}", jenkinsJob.getJobName());
            return jenkinsJob;
        } else {
            String jenkinsJobName = getJenkinsJobName(services, servicesGroup.getServicesEnv());
            log.info("createJenkinsJob new jenkinsJobName {}", jenkinsJobName);
            // 创建jenkinsJob
            jenkinsJob = new JenkinsJob();
            jenkinsJob.setJenkinsServicesId(jenkinsServices.getId());
            jenkinsJob.setJenkinsViewId(jenkinsView.getId());
            jenkinsJob.setJenkinsSshServerId(jenkinsSshServer.getId());
            jenkinsJob.setJobName(jenkinsJobName);
            jenkinsDao.insertJenkinsJob(jenkinsJob);
            Map<String, String> dataMap = new HashMap<>();
            // 检查模板占位keyvalue
            List<JenkinsTemplateParamDTO> templateServicesGroupParams = jenkinsDao.getTemplateParamKeyValueListByDataType(servicesGroup.getJenkinsTemplateId(), SERVICES_DATA_TYPE_SERVICES_GROUP, servicesGroup.getId());
            List<String> errorParamKeys = checkTemplateParamKeyValue(templateServicesGroupParams);
            if (!errorParamKeys.isEmpty()) {
                log.warn("createJenkinsJob new jenkinsJobName {} failed, checkTemplateParamKeyValue errorParamKeys {}", jenkinsJobName, errorParamKeys);
                throw new MicroServiceException(CREATE_JENKINS_JOB_FAILED_SERVICES_GROUP_PARAM_ERROR);
            }
            // 给占位符赋值
            templateServicesGroupParams.forEach(templateParam -> {
                dataMap.put("@" + templateParam.getParamKey() + "@", templateParam.getParamValue());
            });
            // 特殊处理
            dataMap.put("@" + JENKINS_TEMPLATE_KEY_SSH_SERVER_NAME + "@", jenkinsSshServer.getServerName());
            dataMap.put("@" + JENKINS_TEMPLATE_KEY_ACTIVE_PROFILE + "@", servicesGroup.getServicesEnv());
            BaseJenkinsDTO baseDto = new BaseJenkinsDTO(jenkinsServices, jenkinsUser);
            jenkinsApiClient.createJenkinsJob(new CreateJenkinsJobDTO(jenkinsJobName, jenkinsJobTemplateMapper.selectByPrimaryKey(servicesGroup.getJenkinsTemplateId()).getXmlContent(), dataMap), baseDto);
            jenkinsApiClient.addJobToView(jenkinsJob.getJobName(), jenkinsView.getViewName(), baseDto);
            log.info("createJenkinsJob new jenkinsViewName {} success", jenkinsJobName);
            return jenkinsJob;
        }
    }

    /**
     * 根据启动方式，服务运行状态等 分配真正的动作
     *
     * @param servicesGroup
     * @param buildActionEnum
     */
    public List<JenkinsTaskDTO> createJenkinsTaskDTO(ServicesGroup servicesGroup, ServiceGroupBuildActionEnum buildActionEnum) {
        Integer serviceUpdateType = servicesGroup.getServicesUpdateType();
        Integer aliveNum = servicesGroup.getServicesAliveNum();
        List<Services> services = servicesMapper.select(new Services().setServicesGroupId(servicesGroup.getId()).setDelsign(BYTE_0).setStatus(SERVICES_DATA_STATUS_ONLINE));
        // 检查这些服务的真实运行状态
        List<JenkinsTaskDTO> jenkinsTaskDTOS = checkServicesServiceStatus(services, servicesGroup.getCheckAliveType());
        return assignServicesAction(jenkinsTaskDTOS, buildActionEnum, aliveNum, serviceUpdateType);
    }

    /**
     * 根据启动方式，服务运行状态等 分配真正的动作
     *
     * @param services
     * @param buildActionEnum
     */
    public List<JenkinsTaskDTO> createJenkinsTaskDTO(Services services, ServiceGroupBuildActionEnum buildActionEnum) {
        if (!buildActionEnum.equals(ServiceGroupBuildActionEnum.STOP)) {
            // services只支持停止
            throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_SERVICES_ONLY_SUPPORT_STOP);
        }
        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(services.getServicesGroupId());
        Integer serviceUpdateType = servicesGroup.getServicesUpdateType();
        Integer aliveNum = servicesGroup.getServicesAliveNum();
        List<Services> serviceList = Arrays.asList(services);
        // 检查这些服务的真实运行状态
        List<JenkinsTaskDTO> jenkinsTaskDTOS = checkServicesServiceStatus(serviceList, servicesGroup.getCheckAliveType());
        return assignServicesAction(jenkinsTaskDTOS, buildActionEnum, aliveNum, serviceUpdateType);
    }

    @Async("taskExecutor")
    public void executeJenkinsTask(BaseJenkinsDTO baseDto, List<JenkinsTaskDTO> jenkinsTaskDTOS) throws Exception {
        Boolean groupSuccess = true;
        String failedReason = "";
        JenkinsTaskDTO infoDTO = null;
        try {
            for (JenkinsTaskDTO taskDTO : jenkinsTaskDTOS) {
                infoDTO = taskDTO;
                Boolean taskSuccess = false;
                try {
                    if (!restartService.beforeDoTask(taskDTO)) {
                        microserviceCommonService.logTaskContent(taskDTO, "执行beforeDoTask失败");
                        continue;
                    }
                    microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILDING);
                    if (!restartService.doTask(taskDTO, baseDto)) {
                        microserviceCommonService.logTaskContent(taskDTO, "执行doTask失败");
                        continue;
                    }
                    if (!restartService.afterDoTask(taskDTO)) {
                        microserviceCommonService.logTaskContent(taskDTO, "执行afterDoTask失败");
                        continue;
                    }
                    taskSuccess = true;
                } catch (Exception e) {
                    microserviceCommonService.logTaskContent(taskDTO, "执行executeJenkinsTask异常: %s", e.getMessage());
                    log.warn("executeJenkinsTask failed, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    groupSuccess = false;
                    failedReason = e.getMessage();
                    break;
                } finally {
                    if (taskSuccess) {
                        microserviceCommonService.logTaskContent(taskDTO, "任务执行成功");
                    } else {
                        microserviceCommonService.logTaskContent(taskDTO, "任务执行失败");
                    }
                    jenkinsTaskMapper.updateByPrimaryKeySelective(new JenkinsTask().setId(taskDTO.getJenkinsTaskId()).setIsSuccess(StringUtils.booleanToByte(taskSuccess)).setCompleteTime(new Date()).setFailedReason(failedReason));
                }
            }
            jenkinsTaskGroupMapper.updateByPrimaryKeySelective(new JenkinsTaskGroup().setIsSuccess(StringUtils.booleanToByte(groupSuccess)).setCompleteTime(new Date()).setFailedReason(failedReason));
            // servicesGroup下所有services的状态 如果有
            Integer runningServicesNum = servicesDao.getRunningServicesNumByServicesGroupId(infoDTO.getServicesGroupId());
            Integer servicesGroupRunningStatus = !groupSuccess ? SERVICES_GROUP_RUNNING_STATUS_BUILD_ERROR : runningServicesNum > 0 ? SERVICES_GROUP_RUNNING_STATUS_RUNNING : SERVICES_GROUP_RUNNING_STATUS_NOT_RUNNING;
            microserviceCommonService.updateServicesGroupRunningStatus(infoDTO.getServicesGroupId(), servicesGroupRunningStatus);
            log.info("executeJenkinsTask result: {}, taskGroupId {}", groupSuccess, infoDTO.getJenkinsTaskGroupId());
            if (groupSuccess) {
                microserviceCommonService.logTaskGroupContent(infoDTO.getJenkinsTaskGroupId(), infoDTO.getServicesGroupName(), "任务组执行成功");
            } else {
                microserviceCommonService.logTaskGroupContent(infoDTO.getJenkinsTaskGroupId(), infoDTO.getServicesGroupName(), "任务组执行失败");
            }
        } catch (Exception e) {
            log.error("executeJenkinsTask occur exception.", e);
        } finally {
            microserviceCommonService.buildReleaseLock(infoDTO.getJenkinsTaskGroupId());
        }
    }


    /**
     * 根据当前服务运行状态和操作类型 分配具体任务
     *
     * @param jenkinsTaskDTOS
     * @param buildActionEnum
     * @param aliveNum
     * @param serviceUpdateType
     * @return
     */
    private List<JenkinsTaskDTO> assignServicesAction(List<JenkinsTaskDTO> jenkinsTaskDTOS, ServiceGroupBuildActionEnum buildActionEnum, Integer aliveNum, Integer serviceUpdateType) {
        List<JenkinsTaskDTO> orderJenkinsTaskDTOS = new ArrayList<>();
        int index = 1;
        if (buildActionEnum.equals(ServiceGroupBuildActionEnum.STOP)) {
            // 停止指令 将所有运行中的服务停止
            for (JenkinsTaskDTO jenkinsTaskDTO : jenkinsTaskDTOS) {
                if (jenkinsTaskDTO.serviceIsRunning) {
                    jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.STOP.getAction());
                    jenkinsTaskDTO.setIndex(index++);
                    orderJenkinsTaskDTOS.add(jenkinsTaskDTO);
                }
            }
        }
        if (buildActionEnum.equals(ServiceGroupBuildActionEnum.RESTART)) {
            // 如果是普通重启和脚本重启，alive参数没有意义，只需要将所有services都重启一遍
            if (serviceUpdateType.equals(SERVICES_GROUP_UPDATE_TYPE_NORMAL) || serviceUpdateType.equals(SERVICES_GROUP_UPDATE_TYPE_SCRIPT)) {
                for (JenkinsTaskDTO jenkinsTaskDTO : jenkinsTaskDTOS) {
                    if (jenkinsTaskDTO.serviceIsRunning) {
                        jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.RESTART.getAction());
                    } else {
                        jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.START.getAction());
                    }
                    jenkinsTaskDTO.setIndex(index++);
                    orderJenkinsTaskDTOS.add(jenkinsTaskDTO);
                }
            }
            if (serviceUpdateType.equals(SERVICES_GROUP_UPDATE_TYPE_ROLLING) || serviceUpdateType.equals(SERVICES_GROUP_UPDATE_TYPE_ROLLING_WAIT)) {
                // 重启指令
                if (jenkinsTaskDTOS.size() < aliveNum) {
                    // 总实例数小于可用实例数
                    throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_SERVICES_NUM_LESS_THAN_ALIVE_NUM);
                } else if (jenkinsTaskDTOS.size() == aliveNum) {
                    // 总实例数等于可用实例数
                    // 滚服和导流要求实例数量至少有2个
                    if (jenkinsTaskDTOS.size() < 2) {
                        throw new MicroServiceException(BUILD_JENKINS_JOB_FAILED_ALIVE_NUM_LESS_THAN_2);
                    }
                    // 只能依次重启 - 优先找没启动的 兼容aliveNum变大的情况
                    for (JenkinsTaskDTO jenkinsTaskDTO : jenkinsTaskDTOS) {
                        if (!jenkinsTaskDTO.serviceIsRunning) {
                            jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.START.getAction());
                            jenkinsTaskDTO.setIndex(index++);
                            orderJenkinsTaskDTOS.add(jenkinsTaskDTO);
                        }
                    }
                    for (JenkinsTaskDTO jenkinsTaskDTO : jenkinsTaskDTOS) {
                        if (jenkinsTaskDTO.serviceIsRunning) {
                            jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.RESTART.getAction());
                            jenkinsTaskDTO.setIndex(index++);
                            orderJenkinsTaskDTOS.add(jenkinsTaskDTO);
                        }
                    }
                } else {
                    // 总实例数大于可用实例数
                    // 优先找没启动的
                    for (JenkinsTaskDTO jenkinsTaskDTO : jenkinsTaskDTOS) {
                        if (!jenkinsTaskDTO.serviceIsRunning && orderJenkinsTaskDTOS.size() < aliveNum) {
                            jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.START.getAction());
                            jenkinsTaskDTO.setIndex(index++);
                            orderJenkinsTaskDTOS.add(jenkinsTaskDTO);
                        }
                    }
                    // 如果数量还不够aliveNum，那就对运行中的进行restart，再对剩余运行中的进行stop
                    for (JenkinsTaskDTO jenkinsTaskDTO : jenkinsTaskDTOS) {
                        if (jenkinsTaskDTO.serviceIsRunning) {
                            if (orderJenkinsTaskDTOS.size() < aliveNum) {
                                jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.RESTART.getAction());
                            } else {
                                jenkinsTaskDTO.setRealAction(ServiceGroupBuildActionEnum.STOP.getAction());
                            }
                            jenkinsTaskDTO.setIndex(index++);
                            orderJenkinsTaskDTOS.add(jenkinsTaskDTO);
                        }
                    }
                }
            }

        }
        return orderJenkinsTaskDTOS;
    }


    /**
     * 生成jenkinsSshServerName
     * mega-platform-{ecsServer.name}-{1_2_3_4}
     *
     * @param ecsServer
     * @return
     */
    private String getJenkinsSshServerName(EcsServer ecsServer) {
        return String.format("【%s】-%s-%s", JENKINS_PREFIX_MEGA_PLAT, ecsServer.getName(), ecsServer.getPrivateIp().replace(".", "_"));
    }

    /**
     * 生成jenkinsViewName
     * mega-platform-{servicesGroup.servicesEnv}-{project.name}-{servicesGroup.name}-{serviceGroup.id}
     *
     * @param servicesGroup
     * @return
     */
    private String getJenkinsViewName(ServicesGroup servicesGroup) {
        String projectName = projectMapper.selectByPrimaryKey(servicesGroup.getProjectId()).getName();
        return String.format("【%s-%s】-%s-%s-%s", JENKINS_PREFIX_MEGA_PLAT, servicesGroup.getServicesEnv(), projectName, servicesGroup.getName(), servicesGroup.getId());
    }

    /**
     * 生成jenkinsJobName
     * mega-platform-{servicesEnv}-{service.name}-{service.id}
     *
     * @param services
     * @return
     */
    private String getJenkinsJobName(Services services, String servicesEnv) {
        return String.format("【%s-%s】-%s-%s", JENKINS_PREFIX_MEGA_PLAT, servicesEnv, services.getName(), services.getId());
    }

    /**
     * 检查模板是否所有占位都有值
     *
     * @param templateParams
     */
    private List<String> checkTemplateParamKeyValue(List<JenkinsTemplateParamDTO> templateParams) {
        List<String> errorParamKeys = new ArrayList<>();
        for (JenkinsTemplateParamDTO templateParamDTO : templateParams) {
            if (StringUtils.isEmpty(templateParamDTO.getParamValue())) {
                if (!templateParamDTO.getRequired()) {
                    templateParamDTO.setParamValue(templateParamDTO.getDefaultValue());
                }
            }
            if (StringUtils.isEmpty(templateParamDTO.getParamValue())) {
                errorParamKeys.add(templateParamDTO.getParamKey());
            }
        }
        return errorParamKeys;
    }

    /**
     * 检查模板的key是否完全
     *
     * @param templateParams
     * @param jenkinsParams
     * @return
     */
    public List<String> checkTemplateParamKey(List<JenkinsTemplateParamDTO> templateParams, Map<String, String> jenkinsParams) {
        List<String> errorParamKeys = new ArrayList<>();
        for (JenkinsTemplateParamDTO templateParamDTO : templateParams) {
            boolean emptyKey = false;
            if (StringUtils.isEmpty(jenkinsParams.get(templateParamDTO.getParamKey()))) {
                if (!templateParamDTO.getRequired()) {
                    jenkinsParams.put(templateParamDTO.getParamKey(), templateParamDTO.getDefaultValue());
                }
                if (StringUtils.isEmpty(jenkinsParams.get(templateParamDTO.getParamKey()))) {
                    errorParamKeys.add(templateParamDTO.getParamKey());
                    emptyKey = true;
                }
            }
            if (!emptyKey) {
                templateParamDTO.setParamValue(jenkinsParams.get(templateParamDTO.getParamKey()));
            }
        }
        return errorParamKeys;
    }

    /**
     * 检查服务的真实运行状态
     *
     * @param services
     * @param checkAliveType
     * @return
     */
    private List<JenkinsTaskDTO> checkServicesServiceStatus(List<Services> services, Integer checkAliveType) {
        CheckAliveTypeEnum checkAliveTypeEnum = CheckAliveTypeEnum.findByType(checkAliveType);
        List<JenkinsTaskDTO> jenkinsTaskDTOS = new ArrayList<>();
        for (Services service : services) {
            JenkinsTaskDTO jenkinsTaskDTO = new JenkinsTaskDTO();
            jenkinsTaskDTO.setServerIp(servicesDao.getServicesEcsServerPrivateIpByServicesId(service.getId()));
            jenkinsTaskDTO.setServicesPort(Integer.valueOf(microserviceCommonService.getServicesDataJenkinsParamValue(service.getId(), SERVICES_DATA_TYPE_SERVICES, JENKINS_TEMPLATE_KEY_PORT)));
            jenkinsTaskDTO.setJenkinsJobId(service.getJenkinsJobId());
            jenkinsTaskDTO.setServicesId(service.getId());
            jenkinsTaskDTO.setJenkinsJobName(jenkinsJobMapper.selectByPrimaryKey(service.getJenkinsJobId()).getJobName());
            jenkinsTaskDTO.setServicesName(service.getName());
            Boolean isRunning = true;
            if (isRunning && checkAliveTypeEnum.getCheckPort()) {
                isRunning = ServicesCheckUtils.checkServiceIsRunning(jenkinsTaskDTO.getServerIp(), jenkinsTaskDTO.getServicesPort());
            }
            if (isRunning && checkAliveTypeEnum.getCheckConsul()) {
                isRunning = ServicesCheckUtils.checkServiceConsulUp(jenkinsTaskDTO.getServerIp(), jenkinsTaskDTO.getServicesPort());
            }
            if (isRunning && checkAliveTypeEnum.getCheckCustom()) {
                isRunning = ServicesCheckUtils.checkServiceCustomScript(jenkinsTaskDTO.getServerIp(), jenkinsTaskDTO.getServicesPort(), microserviceCommonService.getServicesDataJenkinsParamValue(service.getId(), SERVICES_DATA_TYPE_SERVICES, JENKINS_TEMPLATE_KEY_CUSTOM_SCRIPT));
            }
            if (checkAliveTypeEnum.equals(CheckAliveTypeEnum.NO_CHECK)) {
                isRunning = true;
            }
            jenkinsTaskDTO.setServiceIsRunning(isRunning);
            jenkinsTaskDTOS.add(jenkinsTaskDTO);
        }
        return jenkinsTaskDTOS;
    }
}
