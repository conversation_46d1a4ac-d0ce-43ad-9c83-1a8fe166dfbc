-- -- 第三方账号平台表 account_platform
-- CREATE TABLE `account_platform` (
--   `id` INTEGER PRIMARY KEY AUTO_INCREMENT,
--   `platform_name` VARCHAR(255) COMMENT '平台名称 比如 steam',
--   `callback_url` VARCHAR(255) COMMENT '平台回调URL',
--   `app_id` VARCHAR(255) COMMENT '平台应用ID',
--   `app_secret` VARCHAR(255) COMMENT '平台应用密钥',
--   `delsign` TINYINT NOT NULL DEFAULT 0,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `platform_name_idx` (`platform_name`) USING BTREE
-- ) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='第三方账号平台表';


-- =====================================================
-- ✅ account_db：账号中心数据库
-- =====================================================

CREATE DATABASE IF NOT EXISTS account_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE account_db;

CREATE TABLE `account` (
  `id` BIGINT AUTO_INCREMENT COMMENT '用户唯一标识(自增)',
  `account_name` VARCHAR(128) NOT NULL COMMENT '账号名称(可用于登录)',
  `avatar` VARCHAR(512) COMMENT '用户头像URL',
  `lang_tag` VARCHAR(18) NOT NULL DEFAULT 'en',
  `location` CHAR(3) NOT NULL COMMENT '用户所在国家或地区的ISO 3166-1 alpha-3代码，如CHN(中国)、USA(美国)、GBR(英国)',
  `timezone` VARCHAR(255) NOT NULL DEFAULT 'Asia/Shanghai' COMMENT '用户时区，默认为北京时区(UTC+8)',
  `email` VARCHAR(255),
  `password` CHAR(60)  COMMENT 'bcrypt加密后的密码哈希值',
  `phone_area` INT COMMENT '手机区号，默认86(中国)',
  `phone_number` BIGINT,
  `last_login_time` TIMESTAMP NULL COMMENT '最后登录时间',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_name_idx` (`account_name`) USING BTREE,
  UNIQUE KEY `email_idx` (`email`) USING BTREE,
  UNIQUE KEY `phone_idx` (`phone_number`, `phone_area`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';


-- 表 account与第三方平台绑定表 account_platform_binding
CREATE TABLE `account_platform_binding` (
  `account_id` BIGINT  COMMENT '用户ID',
  `third_platform_id` INTEGER NOT NULL COMMENT '认证渠道ID，关联third_platform表',
  `unique_id` VARCHAR(255) COMMENT '第三方平台账号唯一ID',
  `unique_key` VARCHAR(255) COMMENT '第三方平台账号秘钥',
  `access_token` VARCHAR(255) COMMENT '第三方平台账号访问令牌',
  `refresh_token` VARCHAR(255) COMMENT '第三方平台账号刷新令牌',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`account_id`,`third_platform_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='用户与第三方账号绑定表';

-- 表 用户登录设备表 account_login_device   
CREATE TABLE `account_login_device` (
  `account_id` BIGINT UNSIGNED COMMENT '应用用户表ID',
  `device_uuid`  VARCHAR(128) COMMENT '设备UUID',
  `device_name`  VARCHAR(255) COMMENT '设备名称',
  `device_brand_id`          INT COMMENT '设备品牌id',
  `device_os_id`             INT COMMENT '设备os id', 
  `device_push_channel_id`   INT COMMENT '设备推送渠道id',
  `push_token`   VARCHAR(128) COMMENT '推送token',
  `create_time`  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `delsign` TINYINT NOT NULL DEFAULT 0,
  UNIQUE KEY `account_idx` (`account_id`,`device_uuid`) USING BTREE,
  KEY `push_token_idx` (`push_token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录设备表';


-- 游戏大区表   game_region
CREATE TABLE `game_region` (
  `id` BIGINT  AUTO_INCREMENT COMMENT '游戏大区ID',
  `name` VARCHAR(64)  NOT NULL COMMENT '游戏大区名称如：华东、东南亚',
  `code` VARCHAR(32)  NOT NULL COMMENT '游戏大区代码，如：cn-east',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '游戏大区状态: 0=维护, 1=正常',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='游戏大区表';


-- 游戏服务器表 game_server
CREATE TABLE `game_server` (
  `id` BIGINT  AUTO_INCREMENT COMMENT '游戏服务器ID',
  `game_region_id` BIGINT  COMMENT '游戏大区ID',
  `name` VARCHAR(64)  NOT NULL COMMENT '游戏服务器名称如：华东一区',
  `code` VARCHAR(32) NOT NULL COMMENT '区服编码，如：cn-east-1',
  `type` VARCHAR(64)  COMMENT '游戏服务器类型',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '游戏服务器状态: 0=维护, 1=正常',
  `max_conn` INT COMMENT '游戏服务器最大连接数',
  `conn_count` INT COMMENT '游戏服务器当前连接数',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code_idx` (`code`) USING BTREE,
  CONSTRAINT `fk_game_region` FOREIGN KEY (`game_region_id`) REFERENCES `game_region` (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='游戏服务器表';
 

-- 表 account_character  游戏内用户表
CREATE TABLE `account_character` (
  `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
  `account_id` BIGINT  COMMENT '用户账号ID',
  `character_id`  BIGINT  COMMENT '用户游戏内角色ID',
  `game_region_id`  BIGINT  COMMENT '用户游戏大区ID',
  `game_server_id`  BIGINT  COMMENT '用户游戏服务器ID',
  `character_level` INT COMMENT '角色等级',
  `character_name` VARCHAR(255) COMMENT '角色昵称',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_character_idx` (`account_id`, `character_id`) USING BTREE,
  CONSTRAINT `fk_game_region_char` FOREIGN KEY (`game_region_id`) REFERENCES `game_region` (`id`),
  CONSTRAINT `fk_game_server_char` FOREIGN KEY (`game_server_id`) REFERENCES `game_server` (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='游戏内用户表';

-- --------------------------------------------------------
-- Table structure for 4 排行榜 (Leaderboard)
-- --------------------------------------------------------
-- 排行榜类型
CREATE TABLE `leaderboard_type` (
  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '排行榜类型ID',
  `name` VARCHAR(100) NOT NULL COMMENT '类型名称',
  `interval` INT NOT NULL DEFAULT 60 COMMENT '刷新时间间隔(秒)',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜类型表';

CREATE TABLE `leaderboard` (
  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '排行榜ID',
  `leaderboard_type_id` BIGINT UNSIGNED NOT NULL COMMENT '排行榜类型ID',
  `name` VARCHAR(100) NOT NULL COMMENT '排行榜名称',
  `description` TEXT COMMENT '排行榜描述',
  `game_region_id` BIGINT UNSIGNED NOT NULL COMMENT '游戏区域ID',
  `game_server_id` BIGINT UNSIGNED NOT NULL COMMENT '游戏服务器ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  KEY `idx_name` (`name`),
  KEY `idx_type` (`leaderboard_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜基础信息表';


CREATE TABLE `leaderboard_record` (
  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `leaderboard_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的排行榜ID',
  `account_id` BIGINT UNSIGNED NOT NULL COMMENT '用户账号ID',
  `character_id` BIGINT UNSIGNED NOT NULL COMMENT '用户游戏内角色ID',
  `character_name` VARCHAR(255) COMMENT '用户游戏内角色名称',
  `score` BIGINT NOT NULL COMMENT '分数',
  `rank` INT COMMENT '排名',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` INT NOT NULL DEFAULT 1 COMMENT '版本号',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  KEY `idx_leaderboard` (`leaderboard_id`),
  KEY `idx_account` (`account_id`),
  KEY `idx_character` (`character_id`),
  UNIQUE KEY `idx_unique_record` (`leaderboard_id`, `account_id`, `character_id`, `version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜记录表';