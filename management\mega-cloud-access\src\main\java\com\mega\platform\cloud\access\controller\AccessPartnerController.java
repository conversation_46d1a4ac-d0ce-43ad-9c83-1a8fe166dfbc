package com.mega.platform.cloud.access.controller;

import com.mega.platform.cloud.access.service.AccessPartnerService;
import com.mega.platform.cloud.client.access.AccessPartnerClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.access.AccessPartnerLicenseReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerTelemeringReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerVerifyReqVO;
import com.mega.platform.cloud.data.vo.access.AccessPartnerVerifyRespVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "合作方接口")
@Slf4j
@RestController
public class AccessPartnerController implements AccessPartnerClient {
    private final AccessPartnerService accessPartnerService;

    @Autowired
    public AccessPartnerController(AccessPartnerService accessPartnerService) {
        this.accessPartnerService = accessPartnerService;
    }

    @Override
    public Result<AccessPartnerVerifyRespVO> verify(AccessPartnerVerifyReqVO vo) {
        log.info("收到AES密钥获取请求: tokenKey={}, serverName={}", vo.getAppKey(), vo.getServerName());
        AccessPartnerVerifyRespVO respVO = accessPartnerService.verify(vo);
        log.info("AES密钥获取成功");
        return Results.success(respVO);
    }

    @Override
    public Result<?> license(AccessPartnerLicenseReqVO vo) {
        log.info("收到License验证请求: tokenKey={}, serverName={}, machineId={}", 
                vo.getAppKey(), vo.getServerName(), vo.getMachineId());
        accessPartnerService.license(vo);
        log.info("License验证完成");
        return Results.success();
    }

    @Override
    public Result<?> telemetering(AccessPartnerTelemeringReqVO vo) {
        log.info("收到操作日志上报请求: tokenKey={}, serverName={}", vo.getAppKey(), vo.getServerName());
        accessPartnerService.telemetering(vo);
        log.info("操作日志上报完成");
        return Results.success();
    }
}
