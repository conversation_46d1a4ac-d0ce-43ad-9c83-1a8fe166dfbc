# Feign 内部调用使用说明

## 一、请求方配置
### 1. 在 application-xxx.yml 中添加 Token 请求参数（示例模块：project_app）：
```
    app:
      token:
        key: app_1752202144
        secret: 20b14c39e9094dd3aa262cc6a0f03496
```
### 2.确保对应的 App 拥有访问权限（权限配置模块：project_app_permission）

## 二、被请求方配置
### 1.在启动类 Application 上添加注解，开启 Feign 客户端扫描：
```
    @EnableFeignClients(basePackages = {"com.mega.platform.cloud.client"})
```

### 2.在 application.yml 中添加 JWT 配置：
```
    jwt:
      secret: f2c8f801e3944a06b35897e56bb6db67
      expire-seconds: 3600000
```

### ⚠️ 注意事项：mega-cloud内部调用client中不能使用@RequestMapping

## 三、测试与启动流程
### 启动顺序（确保服务依赖正常注册）：
```text
    access（认证模块） → 被调用模块 → 调用模块
```

### 测试Consul 注册中心地址
```text
    http://192.168.1.52:8500/ui/dc1/services
```
