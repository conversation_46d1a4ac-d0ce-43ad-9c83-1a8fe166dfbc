package com.mega.platform.cloud.monitor.service.services;

import com.mega.platform.cloud.common.enums.CheckAliveTypeEnum;
import com.mega.platform.cloud.common.mapper.ServicesGroupMapper;
import com.mega.platform.cloud.common.mapper.ServicesMapper;
import com.mega.platform.cloud.common.service.CommonFeishuService;
import com.mega.platform.cloud.common.utils.ServicesCheckUtils;
import com.mega.platform.cloud.data.dto.common.feishu.MonitorAlarmServicesGroupRunningStatusFeishuDTO;
import com.mega.platform.cloud.data.dto.common.feishu.MonitorAlarmServicesRunningStatusFeishuDTO;
import com.mega.platform.cloud.data.dto.monitor.ServicesGroupRunningStatusScanDTO;
import com.mega.platform.cloud.data.dto.monitor.ServicesRunningStatusScanDTO;
import com.mega.platform.cloud.data.entity.Services;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import com.mega.platform.cloud.monitor.dao.ServicesDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_0;
import static com.mega.platform.cloud.common.constant.MicroserviceConstants.*;

@Slf4j
@Service
public class ServicesService {

    private final ServicesDao servicesDao;
    private final ServicesMapper servicesMapper;
    private final CommonFeishuService commonFeishuService;
    private final ServicesGroupMapper servicesGroupMapper;

    @Autowired
    public ServicesService(ServicesDao servicesDao, ServicesMapper servicesMapper, CommonFeishuService commonFeishuService, ServicesGroupMapper servicesGroupMapper) {
        this.servicesDao = servicesDao;
        this.servicesMapper = servicesMapper;
        this.commonFeishuService = commonFeishuService;
        this.servicesGroupMapper = servicesGroupMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public void checkServicesGroupRunningStatus(ServicesGroupRunningStatusScanDTO servicesGroupDTO) {
        Integer runningStatus = servicesGroupDTO.getRunningStatus();
        if (runningStatus == null) {
            return;
        }
        Integer realRunningStatus = null;
        Integer dbRealRunningStatus = servicesGroupDTO.getRealRunningStatus();
        // 只处理运行中和未运行的
        if (runningStatus.equals(SERVICES_GROUP_RUNNING_STATUS_RUNNING) || runningStatus.equals(SERVICES_GROUP_RUNNING_STATUS_NOT_RUNNING)) {
            Integer updateType = servicesGroupDTO.getServicesUpdateType();
            List<Services> servicesList = servicesMapper.select(new Services().setDelsign(BYTE_0).setStatus(SERVICES_DATA_STATUS_ONLINE).setServicesGroupId(servicesGroupDTO.getId()));
            long servicesRunningCount = servicesList.stream().filter(services -> services.getRunningStatus().equals(SERVICES_RUNNING_STATUS_RUNNING)).count();
            long servicesNotRunningCount = servicesList.stream().filter(services -> services.getRunningStatus().equals(SERVICES_RUNNING_STATUS_NOT_RUNNING)).count();
            if (updateType.equals(SERVICES_GROUP_UPDATE_TYPE_NORMAL)) {
                // 普通重启 需要保证所有services和servicesGroup状态一直
                if (servicesRunningCount == servicesList.size()) {
                    realRunningStatus = SERVICES_GROUP_RUNNING_STATUS_RUNNING;
                } else if (servicesNotRunningCount == servicesList.size()) {
                    realRunningStatus = SERVICES_GROUP_RUNNING_STATUS_NOT_RUNNING;
                } else {
                    realRunningStatus = SERVICES_GROUP_RUNNING_STATUS_PARTIALLY_RUNNING;
                }
            }
            if (updateType.equals(SERVICES_GROUP_UPDATE_TYPE_ROLLING) || updateType.equals(SERVICES_GROUP_UPDATE_TYPE_ROLLING_WAIT)) {
                // 滚服/导流重启 需要保证aliveNum=正在运行的services数量
                Integer aliveNum = servicesGroupDTO.getServicesAliveNum();
                if (servicesRunningCount >= aliveNum) {
                    realRunningStatus = SERVICES_GROUP_RUNNING_STATUS_RUNNING;
                } else if (servicesRunningCount < aliveNum && servicesRunningCount > 0) {
                    realRunningStatus = SERVICES_GROUP_RUNNING_STATUS_PARTIALLY_RUNNING;
                } else {
                    realRunningStatus = SERVICES_GROUP_RUNNING_STATUS_NOT_RUNNING;
                }
            }
            if (!realRunningStatus.equals(runningStatus) && !realRunningStatus.equals(dbRealRunningStatus)) {
                // 状态不一致 报警
                servicesGroupDTO.setRealRunningStatus(realRunningStatus);
                MonitorAlarmServicesGroupRunningStatusFeishuDTO feishuDTO = new MonitorAlarmServicesGroupRunningStatusFeishuDTO(servicesGroupDTO);
                commonFeishuService.sendMonitorAlarmServiceGroupRunningStatusFeishuMessage(feishuDTO);
            }
            if (!realRunningStatus.equals(dbRealRunningStatus)) {
                // 状态不一致 更新一下数据库
                servicesGroupMapper.updateByPrimaryKeySelective(new ServicesGroup().setId(servicesGroupDTO.getId()).setRealRunningStatus(realRunningStatus));
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void checkServicesRunningStatus(ServicesRunningStatusScanDTO servicesDTO) {
        Integer dbRunningStatus = servicesDTO.getRunningStatus();
        if (dbRunningStatus == null) {
            return;
        }
        Integer dbRealRunningStatus = servicesDTO.getRealRunningStatus();
        // 只处理运行中和未运行的
        if (dbRunningStatus.equals(SERVICES_RUNNING_STATUS_RUNNING) || dbRunningStatus.equals(SERVICES_RUNNING_STATUS_NOT_RUNNING)) {
            CheckAliveTypeEnum checkAliveType = CheckAliveTypeEnum.findByType(servicesDTO.getCheckAliveType());
            Integer realRunningStatus = SERVICES_RUNNING_STATUS_RUNNING;
            if (realRunningStatus.equals(SERVICES_RUNNING_STATUS_RUNNING) && checkAliveType.getCheckConsul()) {
                realRunningStatus = ServicesCheckUtils.checkServiceConsulUp(servicesDTO.getServerIp(), servicesDTO.getPort()) ? SERVICES_RUNNING_STATUS_RUNNING : SERVICES_RUNNING_STATUS_NOT_RUNNING;
            }
            if (realRunningStatus.equals(SERVICES_RUNNING_STATUS_RUNNING) && checkAliveType.getCheckCustom()) {
                String customScript = servicesDao.getServiceParam(servicesDTO.getId(), JENKINS_TEMPLATE_KEY_CUSTOM_SCRIPT);
                servicesDTO.setCustomScript(customScript);
                realRunningStatus = ServicesCheckUtils.checkServiceCustomScript(servicesDTO.getServerIp(), servicesDTO.getPort(), servicesDTO.getCustomScript()) ? SERVICES_RUNNING_STATUS_RUNNING : SERVICES_RUNNING_STATUS_NOT_RUNNING;
            }
            if (realRunningStatus.equals(SERVICES_RUNNING_STATUS_RUNNING) && checkAliveType.getCheckPort()) {
                realRunningStatus = ServicesCheckUtils.checkServiceIsRunning(servicesDTO.getServerIp(), servicesDTO.getPort()) ? SERVICES_RUNNING_STATUS_RUNNING : SERVICES_RUNNING_STATUS_NOT_RUNNING;
            }
            if (!realRunningStatus.equals(dbRunningStatus) && !realRunningStatus.equals(dbRealRunningStatus)) {
                // 状态不一致 报警
                servicesDTO.setRealRunningStatus(realRunningStatus);
                MonitorAlarmServicesRunningStatusFeishuDTO feishuDTO = new MonitorAlarmServicesRunningStatusFeishuDTO(servicesDTO);
                commonFeishuService.sendMonitorAlarmServiceRunningStatusFeishuMessage(feishuDTO);
            }
            if (!realRunningStatus.equals(dbRealRunningStatus)) {
                // 状态不一致 更新一下数据库
                servicesMapper.updateByPrimaryKeySelective(new Services().setId(servicesDTO.getId()).setRealRunningStatus(realRunningStatus));
            }
        }
    }
}
