package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_view")
public class JenkinsView {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * Jenkins视图名称
     */
    @Column(name = "view_name")
    private String viewName;

    /**
     * 所属应用ID
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * appId
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * jenkins服务ID
     */
    @Column(name = "jenkins_services_id")
    private Long jenkinsServicesId;

    /**
     * services_group表id
     */
    @Column(name = "services_group_id")
    private Long servicesGroupId;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}