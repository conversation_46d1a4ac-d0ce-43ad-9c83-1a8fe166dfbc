package com.mega.platform.cloud.common.service;

import com.mega.platform.cloud.common.cache.AppPermissionCache;
import com.mega.platform.cloud.common.constant.JwtConstants;
import com.mega.platform.cloud.data.entity.ProjectAppPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Component
public class AppPermissionService {
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 可配置的白名单路径列表
    private final List<String> publicRoutes = List.of(
            "/**/api/**/public/**",
            "/admin/api/**",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/v3/api-docs/**",
            "/webjars/swagger-ui/**",
            "/actuator/**",
            "/auth/api/test/**",
            "/microservice/api/test/**",
            "/monitor/api/test/**"
    );

    @Autowired
    private AppPermissionCache permissionCache;

    public boolean hasPermission(HttpServletRequest request) {
        String path = request.getRequestURI();
        // 判断是否为白名单路径
        for (String pattern : publicRoutes) {
            if (pathMatcher.match(pattern, path)) {
                return true;
            }
        }

        Long appId = (Long) request.getAttribute(JwtConstants.CLAIM_APP_ID);
        if (appId == null) return false;

        List<ProjectAppPermission> permissions = permissionCache.getPermissions(appId);
        return permissions.stream().anyMatch(p ->
                pathMatcher.match(p.getUrlPattern(), path)
        );
    }
}
