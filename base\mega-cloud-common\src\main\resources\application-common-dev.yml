spring:
  data:
    mongodb:
      uri: mongodb://werewolf-cloud:Mangosteen0!@**************:27017/werewolf?maxPoolSize=500&minPoolSize=10
  cloud:
    consul:
      enabled: true
      host: ************
      port: 8500
      discovery:
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${spring.cloud.client.ip-address}-${server.port}
        service-name: ${spring.application.name}
        health-check-critical-timeout: 1m
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 2
    slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 2
  redis:
    db0:
      host: **************
      port: 6379
      database: 0
      password: LA1954b!
    db3:
      host: **************
      port: 6379
      database: 3
      password: LA1954b!
    db8:
      host: **************
      port: 6379
      database: 8
      password: LA1954b!
    db14:
      host: **************
      port: 6379
      database: 14
      password: LA1954b!
  kafka:
    bootstrap-servers: **************:9992
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: NLAP8pDWhR9Bw5WB
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socket-factory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465

#mybatis:
#  configuration.log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
feishu:
  url:
    common-url: https://open.feishu.cn/open-apis/bot/v2/hook/a9afdba4-e1d6-4fce-aaed-91d21b225d7f
    monitor-alarm-url: https://open.feishu.cn/open-apis/bot/v2/hook/e5371a7c-de21-4015-ad1c-f90d859128b4
    slow-sql-url: https://open.feishu.cn/open-apis/bot/v2/hook/a9afdba4-e1d6-4fce-aaed-91d21b225d7f
    service-running-status-url: https://open.feishu.cn/open-apis/bot/v2/hook/e5371a7c-de21-4015-ad1c-f90d859128b4
mega:
  platform:
    services-id: -1