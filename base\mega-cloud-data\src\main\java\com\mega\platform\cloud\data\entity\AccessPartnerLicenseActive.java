package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "access_partner_license_active")
public class AccessPartnerLicenseActive {
    /**
     * 项目应用ID
     */
    @Id
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 机器唯一标识
     */
    @Id
    @Column(name = "machine_id")
    private String machineId;

    /**
     * 服务名称，如gossipharbor-cloud-exchange
     */
    @Id
    @Column(name = "server_name")
    private String serverName;

    /**
     * License授权码
     */
    @Column(name = "license")
    private String license;

    /**
     * 状态：0-未激活 1-已激活
     */
    @Column(name = "state")
    private Byte state;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标记：0-未删除 1-已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}
