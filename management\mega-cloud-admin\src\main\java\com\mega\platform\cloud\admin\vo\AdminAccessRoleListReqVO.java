package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-角色列表查询请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色列表查询请求")
public class AdminAccessRoleListReqVO {

    // @ApiModelProperty("页码")
    // private Integer pageNum = 1;

    // @ApiModelProperty("每页大小")
    // private Integer pageSize = 10;

    @ApiModelProperty("角色名称")
    private String name;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}