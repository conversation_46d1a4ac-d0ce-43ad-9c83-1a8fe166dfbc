package com.mega.platform.cloud.access.controller;

import com.mega.platform.cloud.access.service.AccessAppService;
import com.mega.platform.cloud.client.access.AccessAppClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenReqVO;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenRespVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "app登录接口")
@Slf4j
@RestController
public class AccessAppController implements AccessAppClient {
    private final AccessAppService accessAppService;
    @Autowired
    public AccessAppController(AccessAppService accessAppService) {
        this.accessAppService = accessAppService;
    }

    @Override
    public Result<AccessAppTokenRespVO> accessAppToken(AccessAppTokenReqVO vo) {
        return Results.success(accessAppService.accessAppToken(vo));
    }
}
