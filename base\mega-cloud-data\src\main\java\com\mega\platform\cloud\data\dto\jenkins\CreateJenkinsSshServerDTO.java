package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreateJenkinsSshServerDTO extends JenkinsGroovyDTO{
    private final static String groovyScriptFormat = "import jenkins.model.*\n" +
            "import jenkins.plugins.publish_over_ssh.*\n" +
            "import jenkins.plugins.publish_over_ssh.ssh.*\n" +
            "\n" +
            "def plugin = Jenkins.instance.getDescriptor(\"jenkins.plugins.publish_over_ssh.BapSshPublisherPlugin\")\n" +
            "\n" +
            "def hostConfig = new BapSshHostConfiguration(\n" +
            "        \"%s\",\n" +
            "        \"%s\",\n" +
            "        \"%s\",\n" +
            "        \"%s\",\n" +
            "        \"/root\",\n" +
            "        %s,\n" +
            "        300000,\n" + "        true,\n" +
            "        null,\n" + "        null,\n" +
            "        false,\n" + "        true,\n" +
            "        null,\n" + "        0,\n" +
            "        null,\n" + "        null,\n" +
            "        null\n" + ")\n" +
            "\n" + "plugin.addHostConfiguration(hostConfig)\n" +
            "plugin.save()\n";

    private String serverName;
    private String hostName;
    private String userName = "root";
    private String password = "Mangosteen0!";
    private Integer port = 22;

    public CreateJenkinsSshServerDTO(String serverName, String hostName) {
        this.serverName = serverName;
        this.hostName = hostName;
    }

    public CreateJenkinsSshServerDTO(String serverName, String hostName, String userName, String password, Integer port) {
        this.serverName = serverName;
        this.hostName = hostName;
        this.userName = userName;
        this.password = password;
        this.port = port;
    }

    @Override
    public String getGroovyScriptStr() {
        return String.format(groovyScriptFormat, serverName, hostName, userName, password, port);
    }
}
