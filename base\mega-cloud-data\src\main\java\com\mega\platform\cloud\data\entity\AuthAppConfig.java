package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "auth_app_config")
public class AuthAppConfig {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 应用ID，关联具体业务系统的标识
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 认证渠道ID，关联third_platform表
     */
    @Column(name = "third_platform_id")
    private Long thirdPlatformId;

    /**
     * 渠道配置参数，格式为JSON
     */
    @Column(name = "config")
    private String config;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 渠道状态：0-启用，1-禁用
     */
    @Column(name = "delsign")
    private Byte delsign;
}