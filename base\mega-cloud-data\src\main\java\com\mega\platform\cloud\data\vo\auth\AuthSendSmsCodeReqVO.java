package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
@Data
@ApiModel("发送短信验证码请求参数")
public class AuthSendSmsCodeReqVO extends BaseReqVO {
    @ApiModelProperty(value = "区号（如 +86）", example = "+86", required = true)
    @NotBlank(message = "区号不能为空")
    private String areaCode;

    @ApiModelProperty(value = "手机号", example = "13800138000", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号格式不正确")
    private String phoneNum;

    /**
     * 用途类型：
     * login - 登录
     * register - 注册
     * reset_pwd - 重置密码
     * bind_phone - 绑定手机号
     */
    @ApiModelProperty(value = "验证码用途类型（login/register/reset_pwd/bind_phone）", example = "login", required = true)
    @NotBlank(message = "验证码用途不能为空")
    private String type;
}
