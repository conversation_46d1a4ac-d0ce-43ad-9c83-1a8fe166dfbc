CREATE TABLE `game_scene` (
  `id` INTEGER PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `scene_code` VARCHAR(255) UNIQUE NOT NULL COMMENT '场景唯一编码，例如 SC001',
  `scene_name` VARCHAR(255) NOT NULL COMMENT '场景名称，例如 餐厅大厅',
  `delsign` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记，0 表示未删除，1 表示已删除'
) COMMENT='游戏场景定义表';


CREATE TABLE `game_decoration_item` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `scene_id` BIGINT NOT NULL COMMENT '关联的场景ID，关联 game_scene.id',
  `item_code` VARCHAR(64) NOT NULL COMMENT '装饰项唯一编码，例如 ITEM001',
  `item_name` VARCHAR(64) NOT NULL COMMENT '装饰项名称，例如 餐桌、地板',
  `delsign` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记，0 表示未删除'
) COMMENT='游戏装饰项定义表';


CREATE TABLE `game_decoration_item` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `scene_id` BIGINT NOT NULL COMMENT '关联的场景ID，关联 game_scene.id',
  `item_code` VARCHAR(64) NOT NULL COMMENT '装饰项唯一编码，例如 ITEM001',
  `item_name` VARCHAR(64) NOT NULL COMMENT '装饰项名称，例如 餐桌、地板',
  `delsign` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标记，0 表示未删除'
) COMMENT='游戏装饰项定义表';

CREATE TABLE `game_decoration_style` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `item_id` BIGINT NOT NULL COMMENT '关联的装饰项ID，关联 game_decoration_item.id',
  `style_code` VARCHAR(64) NOT NULL COMMENT '风格编码，例如 STYLE001',
  `style_name` VARCHAR(64) COMMENT '风格名称',
  `preview_image_url` VARCHAR(255) COMMENT '风格预览图 URL',
  `model_asset_url` VARCHAR(255) COMMENT '风格模型资源地址（3D/动画用）',
  `is_default` BOOLEAN DEFAULT FALSE COMMENT '是否为默认风格',
  `delsign` TINYINT(1) DEFAULT 0 COMMENT '删除标记，0 表示未删除'
) COMMENT='游戏装饰项风格表';

CREATE TABLE `user_decoration_config` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `scene_id` BIGINT NOT NULL COMMENT '场景ID，关联 game_scene.id',
  `item_id` BIGINT NOT NULL COMMENT '装饰项ID，关联 game_decoration_item.id',
  `style_id` BIGINT NOT NULL COMMENT '当前选中的风格ID，关联 game_decoration_style.id',
  `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT='用户当前场景装饰配置表，每条记录表示一个装饰项在当前生效的风格';
