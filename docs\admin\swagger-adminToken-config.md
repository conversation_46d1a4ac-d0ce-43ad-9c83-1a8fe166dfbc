# mega-cloud-admin Swagger adminToken 配置说明

## 问题背景

mega-cloud-admin模组启动时会实例化mega-cloud-common模组中的`DocketConfig.java`，该配置使用的是标准的Bearer Token认证方式（Authorization头）。但是mega-cloud-admin的token认证使用的是自定义的`adminToken`头，因此需要覆盖common模组的Swagger配置。

## 解决方案

### 1. 创建Admin专用Swagger配置

在`management/mega-cloud-admin/src/main/java/com/mega/platform/cloud/admin/config/`目录下创建了`AdminSwaggerConfig.java`：

```java
@Profile({"dev", "test"})
@EnableOpenApi
@Configuration
public class AdminSwaggerConfig {

    @Bean
    public Docket adminApi() {
        return new Docket(DocumentationType.OAS_30)
                .groupName("admin") // 指定唯一组名避免冲突
                // 配置使用adminToken认证
    }

    private ApiKey adminToken() {
        return new ApiKey("adminToken", "adminToken", "header");
    }
}
```

### 2. 关键配置说明

#### 2.1 避免冲突机制
- 使用`groupName("admin")`指定唯一的组名
- 避免与common模组的默认组名冲突
- 两个Docket可以并存，但Admin组优先显示

#### 2.2 认证配置
- **认证方式**: ApiKey
- **参数名**: adminToken
- **传递方式**: header
- **显示名**: adminToken

#### 2.3 安全上下文
- 自动排除`/public/`路径的接口，无需认证
- 其他接口（`/system/`和`/{projectId}/`）都需要adminToken认证

### 3. 与现有认证系统的兼容性

#### 3.1 AdminAuthTokenFilter兼容
Admin模组的`AdminAuthTokenFilter`从请求头中提取token的代码：
```java
private String extractToken(HttpServletRequest request) {
    String adminToken = request.getHeader("adminToken");  // 使用adminToken头
    // ...
}
```

#### 3.2 认证流程
1. 用户在Swagger UI中点击"Authorize"按钮
2. 输入JWT token（不需要Bearer前缀）
3. Swagger自动在请求头中添加`adminToken: <jwt_token>`
4. AdminAuthTokenFilter从`adminToken`头中提取token进行验证

### 4. 使用方法

#### 4.1 启动应用
```bash
cd management/mega-cloud-admin
mvn spring-boot:run
```

#### 4.2 访问Swagger UI
```
http://localhost:8081/swagger-ui/index.html
```

#### 4.3 配置认证
1. 点击页面右上角的"Authorize"按钮
2. 在"adminToken"输入框中输入JWT token
3. 点击"Authorize"确认
4. 点击"Close"关闭对话框

#### 4.4 测试接口
提供了测试接口验证配置：
- `POST /admin/api/public/swagger-test/hello` - 公开接口，无需认证
- `POST /admin/api/system/swagger-test/protected` - 系统级接口，需要adminToken
- `POST /admin/api/{projectId}/swagger-test/project` - 项目级接口，需要adminToken和项目权限

### 5. 配置优势

#### 5.1 完全兼容
- 与现有的AdminAuthTokenFilter完全兼容
- 不需要修改任何现有的认证逻辑
- 保持与其他模组的独立性

#### 5.2 用户友好
- 在Swagger UI中直接显示"adminToken"，用户一目了然
- 不需要手动添加"Bearer "前缀
- 自动应用到所有需要认证的接口

#### 5.3 安全性
- 自动识别公开接口，无需手动配置
- 支持系统级和项目级权限验证
- 与现有权限系统无缝集成

### 6. 技术实现细节

#### 6.1 组名隔离
```java
@Bean
public Docket adminApi() {
    return new Docket(DocumentationType.OAS_30)
            .groupName("admin") // 指定唯一组名
            // Admin专用配置
}
```

#### 6.2 安全上下文选择器
```java
.operationSelector(operationContext -> {
    String path = operationContext.requestMappingPattern();
    return path != null && !path.contains("/public/");
})
```

#### 6.3 ApiKey配置
```java
private ApiKey adminToken() {
    return new ApiKey("adminToken", "adminToken", "header");
    //           显示名称    参数名称     传递方式
}
```

### 7. 验证方法

#### 7.1 编译验证
```bash
cd management/mega-cloud-admin
mvn compile
```

#### 7.2 启动验证
启动应用后检查日志，确认没有Bean冲突错误。

#### 7.3 功能验证
1. 访问Swagger UI
2. 确认"Authorize"按钮显示正确
3. 配置adminToken后测试接口调用

### 8. 注意事项

1. **环境限制**: 配置仅在dev和test环境生效
2. **Token格式**: 输入的token应为标准JWT格式
3. **权限验证**: 项目级接口还需要相应的项目权限
4. **缓存清理**: 修改配置后建议清理浏览器缓存

## 总结

通过创建Admin专用的Swagger配置，成功实现了：
- 覆盖common模组的默认配置
- 使用adminToken作为认证头
- 与现有认证系统完全兼容
- 提供用户友好的API测试界面

这个解决方案遵循了Spring Boot的Bean优先级机制，既保持了模组间的独立性，又满足了Admin模组的特殊认证需求。
