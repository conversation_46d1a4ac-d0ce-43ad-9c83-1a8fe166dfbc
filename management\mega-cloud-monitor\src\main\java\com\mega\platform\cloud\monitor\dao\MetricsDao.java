package com.mega.platform.cloud.monitor.dao;

import com.mega.platform.cloud.data.dto.monitor.MetricsAlarmDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsNotifyDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsRuleDTO;
import com.mega.platform.cloud.data.entity.MonitorAlarm;
import com.mega.platform.cloud.data.entity.MonitorMetrics;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface MetricsDao {
    @MapKey("id")
    Map<Long, MonitorMetrics> getMonitorMetricsMapByIds(@Param("ids") List<Long> ids);

    List<MetricsRuleDTO> getMetricsRuleDTOScanList();

    List<MetricsAlarmDTO> getMetricsAlarmDTOScanList();

    void insertMonitorAlarm(@Param("monitorAlarm") MonitorAlarm monitorAlarm);

    List<MetricsNotifyDTO> getMetricsNotifyDTOByRuleId(@Param("monitorRuleId") Long monitorRuleId);

    MetricsRuleDTO getMetricsRuleDTOByRuleId(@Param("ruleId") Long ruleId);
}