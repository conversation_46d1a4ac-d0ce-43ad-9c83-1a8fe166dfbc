package com.mega.platform.cloud.access.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mega.platform.cloud.AccessErrorCode;
import com.mega.platform.cloud.AccessException;
import com.mega.platform.cloud.access.dao.AccessPartnerDao;
import com.mega.platform.cloud.core.utils.JsonUtils;
import com.mega.platform.cloud.data.entity.AccessPartnerCrypto;
import com.mega.platform.cloud.data.entity.AccessPartnerLicenseActive;
import com.mega.platform.cloud.data.entity.AccessPartnerLicenseActiveRecord;
import com.mega.platform.cloud.data.entity.ProjectApp;
import com.mega.platform.cloud.data.vo.access.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccessPartnerService {
    private final AccessPartnerDao accessPartnerDao;

    @Autowired
    public AccessPartnerService(AccessPartnerDao accessPartnerDao) {
        this.accessPartnerDao = accessPartnerDao;
    }

    /**
     * AES密钥获取接口
     *
     * @param reqVO 请求参数
     * @return AES密钥响应
     */
    public AccessPartnerVerifyRespVO verify(AccessPartnerVerifyReqVO reqVO) {
        log.info("AES密钥获取请求: appKey={}, serverName={}", reqVO.getAppKey(), reqVO.getServerName());

        // 1. 验证appKey，查询项目应用信息
        ProjectApp projectApp = accessPartnerDao.selectProjectAppByappKey(reqVO.getAppKey());
        if (projectApp == null) {
            log.warn("无效的appKey: {}", reqVO.getAppKey());
            throw new AccessException(AccessErrorCode.ERR_4000);
        }

        // 2. 查询加密信息
        AccessPartnerCrypto crypto = accessPartnerDao.selectCryptoByProjectAppIdAndServerName(
                projectApp.getId(), reqVO.getServerName());
        if (crypto == null) {
            log.warn("未找到加密信息: projectAppId={}, serverName={}", projectApp.getId(), reqVO.getServerName());
            throw new AccessException(AccessErrorCode.ERR_4000);
        }

        // 3. 验证MD5
        if (StringUtils.hasText(crypto.getMd5Json())) {
            // 解析存储的MD5信息
            Map<String, String> storedMd5Map;
            try {
                storedMd5Map = JsonUtils.fromJson(crypto.getMd5Json(), new TypeReference<Map<String, String>>() {});
            } catch (Exception e) {
                log.error("解析MD5 JSON失败: {}", crypto.getMd5Json(), e);
                throw new AccessException(AccessErrorCode.ERR_4001);
            }

            // 将请求的MD5列表转换为Map
            Map<String, String> requestMd5Map = reqVO.getMd5List().stream()
                    .collect(Collectors.toMap(AccessMd5VO::getFileName, AccessMd5VO::getMd5));

            // 比较MD5
            for (Map.Entry<String, String> entry : storedMd5Map.entrySet()) {
                String fileName = entry.getKey();
                String storedMd5 = entry.getValue();
                String requestMd5 = requestMd5Map.get(fileName);

                if (!storedMd5.equals(requestMd5)) {
                    log.warn("MD5验证失败: fileName={}, stored={}, request={}", fileName, storedMd5, requestMd5);
                    throw new AccessException(AccessErrorCode.ERR_4001);
                }
            }
        }

        // 4. 返回密钥
        AccessPartnerVerifyRespVO respVO = new AccessPartnerVerifyRespVO();
        respVO.setKey(crypto.getCryptoKey());
        
        log.info("AES密钥获取成功: projectAppId={}, serverName={}", projectApp.getId(), reqVO.getServerName());
        return respVO;
    }

    /**
     * License验证接口
     *
     * @param reqVO 请求参数
     */
    public void license(AccessPartnerLicenseReqVO reqVO) {
        log.info("License验证请求: appKey={}, serverName={}, machineId={}", 
                reqVO.getAppKey(), reqVO.getServerName(), reqVO.getMachineId());

        // 1. 验证appKey，查询项目应用信息
        ProjectApp projectApp = accessPartnerDao.selectProjectAppByappKey(reqVO.getAppKey());
        if (projectApp == null) {
            log.warn("无效的appKey: {}", reqVO.getAppKey());
            throw new AccessException(AccessErrorCode.ERR_4000);
        }

        // 2. 查询License激活状态
        AccessPartnerLicenseActive existingActive = accessPartnerDao.selectLicenseActiveByProjectAppIdAndMachineIdAndServerName(
                projectApp.getId(), reqVO.getMachineId(), reqVO.getServerName());

        Date now = new Date();
        AccessPartnerLicenseActiveRecord record = new AccessPartnerLicenseActiveRecord()
                .setProjectAppId(projectApp.getId())
                .setInnerIp(reqVO.getInnerIp())
                .setPublicIp(reqVO.getPublicIp())
                .setMachineId(reqVO.getMachineId())
                .setLicense(reqVO.getLicense())
                .setServerName(reqVO.getServerName())
                .setCreateTime(now)
                .setUpdateTime(now);

        if (existingActive == null) {
            // 首次激活
            log.info("首次激活License: projectAppId={}, machineId={}, serverName={}", 
                    projectApp.getId(), reqVO.getMachineId(), reqVO.getServerName());

            AccessPartnerLicenseActive newActive = new AccessPartnerLicenseActive()
                    .setProjectAppId(projectApp.getId())
                    .setMachineId(reqVO.getMachineId())
                    .setServerName(reqVO.getServerName())
                    .setLicense(reqVO.getLicense())
                    .setState((byte) 1)
                    .setCreateTime(now)
                    .setUpdateTime(now)
                    .setDelsign((byte) 0);

            accessPartnerDao.insertLicenseActive(newActive);
            
            record.setSuccess((byte) 1);
            accessPartnerDao.insertLicenseActiveRecord(record);
            
            log.info("License首次激活成功");
        } else {
            // 验证已存在的激活记录
            if (!existingActive.getMachineId().equals(reqVO.getMachineId()) || 
                !existingActive.getLicense().equals(reqVO.getLicense())) {
                
                String failReason = String.format("machineId或license不匹配: 存储的machineId=%s, 请求的machineId=%s, 存储的license=%s, 请求的license=%s",
                        existingActive.getMachineId(), reqVO.getMachineId(),
                        existingActive.getLicense(), reqVO.getLicense());
                
                log.warn("License验证失败: {}", failReason);
                
                record.setSuccess((byte) 0).setFailReason(failReason);
                accessPartnerDao.insertLicenseActiveRecord(record);
                
                throw new AccessException(AccessErrorCode.ERR_4002);
            } else {
                // 验证通过，更新状态
                existingActive.setState((byte) 1).setUpdateTime(now);
                accessPartnerDao.updateLicenseActive(existingActive);
                
                record.setSuccess((byte) 1);
                accessPartnerDao.insertLicenseActiveRecord(record);
                
                log.info("License验证成功");
            }
        }
    }

    /**
     * 操作日志上报接口
     *
     * @param reqVO 请求参数
     */
    public void telemetering(AccessPartnerTelemeringReqVO reqVO) {
        log.info("操作日志上报请求: appKey={}, serverName={}", reqVO.getAppKey(), reqVO.getServerName());

        // 1. 验证appKey，查询项目应用信息
        ProjectApp projectApp = accessPartnerDao.selectProjectAppByappKey(reqVO.getAppKey());
        if (projectApp == null) {
            log.warn("无效的appKey: {}", reqVO.getAppKey());
            throw new AccessException(AccessErrorCode.ERR_4000);
        }

        // 2. 处理日志数据（当前版本仅记录日志，不做具体处理）
        log.info("接收到操作日志: projectAppId={}, serverName={}, logData={}", 
                projectApp.getId(), reqVO.getServerName(), reqVO.getLogData());
        
        log.info("操作日志上报处理完成");
    }
}
