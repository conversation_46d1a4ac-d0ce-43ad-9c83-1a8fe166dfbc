package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_job_template_param")
public class JenkinsJobTemplateParam {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 模板ID，关联jenkins_job_template(id)
     */
    @Column(name = "jenkins_template_id")
    private Long jenkinsTemplateId;

    /**
     * 参数key（如user、env、server_list）
     */
    @Column(name = "param_key")
    private String paramKey;

    /**
     * 参数名称（用于页面显示）
     */
    @Column(name = "param_name")
    private String paramName;

    /**
     * 参数类型 1-group上的 2-service上的
     */
    @Column(name = "services_data_type")
    private Integer servicesDataType;

    /**
     * 是否必填，1=必填 0=选填
     */
    @Column(name = "required")
    private Byte required;

    /**
     * 默认值（可选）
     */
    @Column(name = "default_value")
    private String defaultValue;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 0-后台参数 1-普通参数
     */
    @Column(name = "is_visible")
    private Byte isVisible;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}