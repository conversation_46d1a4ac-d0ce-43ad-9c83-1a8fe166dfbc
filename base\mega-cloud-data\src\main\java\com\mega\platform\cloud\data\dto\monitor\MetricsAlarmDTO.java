package com.mega.platform.cloud.data.dto.monitor;

import com.mega.platform.cloud.data.entity.MonitorAlarm;
import com.mega.platform.cloud.data.entity.MonitorRule;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class MetricsAlarmDTO extends MonitorAlarm {
    private Integer alarmIntervalSecond;
    private BigDecimal currentRatio;
    private String sourceName;
    private Integer sourceType;
    private Long sourceId;
}
