package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel(value = "服务上线下线请求参数")
public class AdminServicesStatusEditReqVO {
    @ApiModelProperty(value = "更新服务id", required = true)
    @NotNull
    private Long servicesId;

    @ApiModelProperty(value = "上线-1 下线-0", required = true)
    @NotNull
    private Integer status;
}
