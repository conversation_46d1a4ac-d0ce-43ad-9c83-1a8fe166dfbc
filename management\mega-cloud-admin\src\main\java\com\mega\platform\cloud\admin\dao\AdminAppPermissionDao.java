package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.vo.AdminAppPermissionListRespVO;
import com.mega.platform.cloud.admin.vo.AdminUrlPatternListRespVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * App访问平台权限数据访问层
 */
@Mapper
public interface AdminAppPermissionDao {
    
    /**
     * 查询路由配置列表
     * 查询project_url_pattern表中所有未删除的记录
     * 
     * @return 路由配置列表
     */
    List<AdminUrlPatternListRespVO> selectUrlPatternList();
    
    /**
     * 查询App权限列表
     * 查询project_app_permission表并关联project_app表获取App名称
     * 
     * @return App权限列表
     */
    List<AdminAppPermissionListRespVO> selectAppPermissionList();
}
