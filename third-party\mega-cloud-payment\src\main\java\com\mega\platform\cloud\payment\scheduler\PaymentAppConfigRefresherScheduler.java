package com.mega.platform.cloud.payment.scheduler;

import com.mega.platform.cloud.common.mapper.PaymentAppConfigMapper;
import com.mega.platform.cloud.data.entity.PaymentAppConfig;
import com.mega.platform.cloud.payment.cache.PaymentAppConfigCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentAppConfigRefresherScheduler {
    private final PaymentAppConfigMapper configMapper;
    private final PaymentAppConfigCache configCache;

    @Scheduled(fixedDelay = 5 * 60 * 1000) // 每 5 分钟刷新
    public void refresh() {
        List<PaymentAppConfig> list = configMapper.select(new PaymentAppConfig().setDelsign((byte) 0));
        configCache.update(list);
        log.info("刷新 payment_app_config 缓存，共加载 {} 条记录", list.size());
    }
}
