package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.common.mapper.*;
import com.mega.platform.cloud.data.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminBaseService {
    private final DicMapper dicMapper;
    private final ServicesLogFormatMapper servicesLogFormatMapper;
    private final JenkinsServicesMapper jenkinsServicesMapper;
    private final JenkinsJobTemplateMapper jenkinsJobTemplateMapper;
    private final JenkinsJobTemplateParamMapper jenkinsJobTemplateParamMapper;
    private final EcsServerMapper ecsServerMapper;
    private final ProjectAppMapper projectAppMapper;

    public AdminBaseDicListRespVO getDicList(AdminBaseDicListReqVO reqVO) {
        // 判空
        List<Long> dicCateIds = reqVO.getDicCateIds();
        if (CollectionUtils.isEmpty(dicCateIds)) {
            return new AdminBaseDicListRespVO().setDicMap(Collections.emptyMap());
        }

        // 构造 Example 查询条件
        Example example = new Example(Dic.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("dicCateId",  dicCateIds).andEqualTo(new Dic().setDelsign((byte) 0));
        List<Dic> dicList = dicMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(dicList)) {
            return new AdminBaseDicListRespVO().setDicMap(Collections.emptyMap());
        }
        Map<Long, List<Dic>> dicMap = dicList.stream().collect(Collectors.groupingBy(Dic::getDicCateId));
        return new AdminBaseDicListRespVO().setDicMap(dicMap);
    }

    public AdminBaseLogFormatListRespVO getLogFormatList() {
        List<ServicesLogFormat> servicesLogFormats = servicesLogFormatMapper.select(new ServicesLogFormat().setDelsign((byte) 0));
        return new AdminBaseLogFormatListRespVO().setServicesLogFormats(servicesLogFormats);
    }

    public AdminBaseJenkinsInstancesListRespVO getJenkinsInstancesList() {
        List<JenkinsServices> jenkinsServices =  jenkinsServicesMapper.select(new JenkinsServices().setDelsign((byte) 0));
        return new AdminBaseJenkinsInstancesListRespVO().setJenkinsServices(jenkinsServices);
    }

    public AdminBaseJenkinsTemplateListRespVO getJenkinsTemplateList() {
        List<JenkinsJobTemplate> jenkinsJobTemplates = jenkinsJobTemplateMapper.select(new JenkinsJobTemplate().setDelsign((byte) 0));
        return new AdminBaseJenkinsTemplateListRespVO().setJenkinsJobTemplates(jenkinsJobTemplates);
    }

    public AdminBaseJenkinsTemplateParamListRespVO getJenkinsTemplateListParam(AdminBaseJenkinsTemplateParamListReqVO reqVO) {
        List<JenkinsJobTemplateParam> jenkinsJobTemplateParams = jenkinsJobTemplateParamMapper.select(new JenkinsJobTemplateParam()
                .setJenkinsTemplateId(reqVO.getJenkinsTemplateId()).setServicesDataType(reqVO.getServicesDataType()).setDelsign((byte) 0));
        return new AdminBaseJenkinsTemplateParamListRespVO().setJenkinsJobTemplateParams(jenkinsJobTemplateParams);
    }

    public AdminBaseEcsServerListRespVO getEcsServerList(Long projectId) {
        List<EcsServer> ecsServers;
        if (projectId == -1) {
            ecsServers = ecsServerMapper.select(new EcsServer().setDelsign((byte) 0));
        } else {
            ecsServers = ecsServerMapper.select(new EcsServer().setProjectId(projectId).setDelsign((byte) 0));
        }
        return new AdminBaseEcsServerListRespVO().setEcsServerList(ecsServers);
    }
}
