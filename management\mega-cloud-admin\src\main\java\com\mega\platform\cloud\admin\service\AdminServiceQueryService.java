package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminServiceQueryDao;
import com.mega.platform.cloud.admin.dto.AdminServicesJenkinsParamDTO;
import com.mega.platform.cloud.admin.dto.AdminServicesTagDTO;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.AdminErrorCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务查询业务逻辑层
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceQueryService {

    
    private final AdminServiceQueryDao adminServiceQueryDao;

    /**
     * 查询全部services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    public List<AdminServicesListRespVO> getServicesList(Long projectId, AdminServicesListReqVO reqVO) {

            // 1 查询总信息
            List<AdminServicesListRespVO> result = adminServiceQueryDao.selectServicesList(projectId, reqVO);
            if (result == null || result.isEmpty()) {
                return new ArrayList<>();
            }

            log.info("查询全部services列表成功，projectId: {}, 结果数量: {}", projectId, result.size());
            // 遍历result，把所有servicesId存入Set<Long> ,也把servicesGroupId存入Set<Long>
            Set<Long> servicesIds = new HashSet<>();
            Set<Long> servicesGroupIds = new HashSet<>();
            for (AdminServicesListRespVO item : result) {
                servicesIds.add(item.getServicesId());
                servicesGroupIds.add(item.getServicesGroupId());
            }

            // 2 查询tags
            Map<Long, List<Long>> servicesTagsMap = getServicesTagsMap(servicesGroupIds);

            // 3 查询services params 
            Map<Long, Map<String,String>> servicesParamsMap = getServicesParams(servicesIds);

            // 4 查询services group params
            Map<Long, Map<String,String>> servicesGroupParamsMap = getServicesGroupParams(servicesGroupIds);
            
            // 遍历result赋值
            for (AdminServicesListRespVO item : result) {   
                List<Long> tags = servicesTagsMap.get(item.getServicesGroupId());
                item.setTags(tags != null ? tags : new ArrayList<>());
                
                Map<String,String> servicesParams = servicesParamsMap.get(item.getServicesId());
                item.setServicesParams(servicesParams != null ? servicesParams : new HashMap<>());
                
                Map<String,String> groupParams = servicesGroupParamsMap.get(item.getServicesGroupId());
                item.setServicesGroupParams(groupParams != null ? groupParams : new HashMap<>());
            }

            return result;
    }
            

    /**
     * 获取services标签map
     * @param servicesGroupIds
     * @return
     */
    private Map<Long, List<Long>> getServicesTagsMap( Set<Long> servicesGroupIds) {
        List<AdminServicesTagDTO> tagList = adminServiceQueryDao.selectServicesTags(servicesGroupIds);
        // 生成map key为 servicesGroupId, value为tagId列表
        return tagList.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesTagDTO::getServiceGroupId, 
                        Collectors.mapping(AdminServicesTagDTO::getTagId, Collectors.toList()))
                        ); 
    }

    private Map<Long, Map<String,String>> getServicesParams(Set<Long> servicesIds){
        List<AdminServicesJenkinsParamDTO> servicesParams = adminServiceQueryDao.selectServicesParams(servicesIds);
        // 生成map key为servicesId , value为paramKey和paramValue组成的map
        return servicesParams.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesJenkinsParamDTO::getId, 
                        Collectors.toMap(AdminServicesJenkinsParamDTO::getParamKey, AdminServicesJenkinsParamDTO::getParamValue))
                        );
    }

    private Map<Long, Map<String, String>> getServicesGroupParams(Set<Long> servicesGroupIds) {
        List<AdminServicesJenkinsParamDTO> servicesGroupParams = adminServiceQueryDao.selectServicesGroupParams(servicesGroupIds);
        // 生成map key为servicesGroupId , value为paramKey和paramValue组成的map
        return servicesGroupParams.stream()
                      .collect(
                        Collectors.groupingBy(AdminServicesJenkinsParamDTO::getId, 
                        Collectors.toMap(AdminServicesJenkinsParamDTO::getParamKey, AdminServicesJenkinsParamDTO::getParamValue))
                        );
    }


    /**
     * 基于组查询services列表
     * @param projectId 项目ID
     * @param reqVO 查询条件
     * @return services列表
     */
    public List<AdminServicesListByGroupRespVO> getServicesListByGroup(Long projectId, AdminServicesListByGroupReqVO reqVO) {
        List<AdminServicesListByGroupRespVO> result = adminServiceQueryDao.selectServicesListByGroup(projectId, reqVO.getServicesGroupId());
        if(result == null || result.isEmpty()){
            return new ArrayList<>();
        }
        Set<Long> servicesIds = new HashSet<>();
        for (AdminServicesListByGroupRespVO item : result) {
            servicesIds.add(item.getServicesId());
        }

        Map<Long, Map<String,String>> servicesParamsMap = getServicesParams(servicesIds);
        for (AdminServicesListByGroupRespVO item : result) {   
            Map<String,String> servicesParams = servicesParamsMap.get(item.getServicesId());
            item.setServicesParams(servicesParams != null ? servicesParams : new HashMap<>());
        }

        log.info("基于组查询services列表成功，projectId: {}, servicesGroupId: {}, 结果数量: {}", 
                    projectId, reqVO.getServicesGroupId(), result.size());
        return result;
       
    }
}