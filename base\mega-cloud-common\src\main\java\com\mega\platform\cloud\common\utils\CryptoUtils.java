package com.mega.platform.cloud.common.utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class CryptoUtils {

    private static final String ALGORITHM = "AES"; // 加密算法
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding"; // 加密模式

    /**
     * 生成随机 AES 密钥
     */
    public static String generateKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        keyGenerator.init(128); // 设置密钥长度为 128 位
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    /**
     * AES 加密
     */
    public static String aesEncrypt(String data, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(key), ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encryptedData = cipher.doFinal(data.getBytes("UTF-8"));
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    /**
     * AES 解密
     */
    public static String aesDecrypt(String encryptedData, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(key), ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] originalData = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(originalData, "UTF-8");
    }

    private static final String ALPHABET = "abcdefghijklmnopqrstuvwxyz0123456789";

    /**
     * 对字符串进行编码，结果长度为 16 位，仅包含小写字母和数字
     *
     * @param input 原始字符串
     * @return 16 位编码字符串
     */
    public static String md5EncodeTo16Characters(String input) {
        try {
            // 使用 MD5 生成哈希值
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes());

            // 转换哈希值为 16 位仅包含小写字母和数字的编码
            StringBuilder encoded = new StringBuilder();
            for (int i = 0; i < hash.length && encoded.length() < 16; i++) {
                // 取每个字节的无符号值
                int index = Byte.toUnsignedInt(hash[i]) % ALPHABET.length();
                encoded.append(ALPHABET.charAt(index));
            }

            return encoded.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无法生成编码", e);
        }
    }

    public static PrivateKey privateKeyGenerator(String privateKeyPEM) {
        try {
            privateKeyPEM = privateKeyPEM.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
            byte[] keyBytes = Base64.getDecoder().decode(privateKeyPEM);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
            return privateKey;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        try {
//            // 测试数据
//            String data = "Hello, AES Encryption!";
//            System.out.println("原始数据: " + data);
//
//            // 生成密钥
//            String key = generateKey();
//            System.out.println("生成的密钥: " + key);
//
//            // 加密
//            String encryptedData = encrypt(data, key);
//            System.out.println("加密后的数据: " + encryptedData);
//
            // 解密
            String decryptedData = aesDecrypt("7Ox/S2ipVu64dRmcxtCZAWqqHdGqBeQrAK4ZWmZbCXRkAzyOK/X407HrQE7sjW5qUxPimHZTl5nO8x9k38rjlupA4aVjJNsiA9cAQCB67TA0CKOVZza+0FQCrWbL0f4V+tTHwqjOTJ5pkiFhqZrQU/9mChiaFK7ya3hWWildrxiFB//s/zaJ+6mFFjyb/wrNzhMzFP1xATHOfE4NdayJTwIzVFpWuKZzwYmW6pXmTG7sz+BXquOOwXjsH9P5MlhV", "uY36cIabw+LgpsFwOjX8Eg==");
            System.out.println("解密后的数据: " + decryptedData);
//            System.out.println(md5EncodeTo16Characters("sssvaebeiuabhvwaibneailbnaelbjaeibss"));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}