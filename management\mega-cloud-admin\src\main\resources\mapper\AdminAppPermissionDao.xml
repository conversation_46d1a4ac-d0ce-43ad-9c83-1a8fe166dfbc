<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminAppPermissionDao">
    
    <!-- 查询路由配置列表 -->
    <select id="selectUrlPatternList" resultType="com.mega.platform.cloud.admin.vo.AdminUrlPatternListRespVO">
        SELECT 
            id,
            name,
            url_pattern AS urlPattern,
            remark,
            create_time AS createTime,
            update_time AS updateTime,
            delsign
        FROM project_url_pattern
        WHERE delsign = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 查询App权限列表 -->
    <select id="selectAppPermissionList" resultType="com.mega.platform.cloud.admin.vo.AdminAppPermissionListRespVO">
        SELECT 
            pap.id,
            pap.project_app_id AS projectAppId,
            pa.name AS projectAppName,
            pap.url_pattern AS urlPattern,
            pap.create_time AS createTime,
            pap.update_time AS updateTime,
            pap.delsign
        FROM project_app_permission pap
        LEFT JOIN project_app pa ON pap.project_app_id = pa.id
        WHERE pap.delsign = 0
        ORDER BY pap.create_time DESC
    </select>
    
</mapper>
