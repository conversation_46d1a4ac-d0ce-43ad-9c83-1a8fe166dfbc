package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 管理员登录请求参数
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("管理员登录请求参数")
public class AdminAuthLoginReqVO {

    @ApiModelProperty(value = "账号", example = "admin")
    private String username;

    @ApiModelProperty(value = "密码", example = "123456")
    private String password;

}