package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 权限管理-角色用户绑定列表响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色用户绑定列表响应参数")
public class AdminAccessRoleUserBindingListRespVO {
    @ApiModelProperty(value = "角色ID", example = "1")
    private Long adminRoleId;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long adminUserId;

    @ApiModelProperty(value = "用户名", example = "admin")
    private String adminUserName;

    @ApiModelProperty(value = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;

}