package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理-角色用户绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色用户绑定更新请求参数")
public class AdminAccessRoleUserBindingUpdateReqVO {

    @ApiModelProperty(value = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    @ApiModelProperty(value = "用户ID", example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long adminUserId;

    @ApiModelProperty(value = "删除标识", example = "0")
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;

}