package com.mega.platform.cloud.admin.vo;

import com.mysql.fabric.ServerGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("服务组列表返回项")
public class AdminServicesGroupRespVO {
    @ApiModelProperty("服务组ID")
    private Long servicesGroupId;

    @ApiModelProperty("服务组名称")
    private String servicesGroupName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("管理员信息")
    private String adminUserId;

    @ApiModelProperty("是否自研")
    private Integer isSelf;

    @ApiModelProperty("上线状态")
    private Integer onlineStatus;

    @ApiModelProperty("运行状态")
    private Integer runStatus;

    @ApiModelProperty("真实运行状态")
    private Integer realRunStatus;

    @ApiModelProperty("标签列表 逗号分隔")
    private String tagIds; // 标签ID列表
}
