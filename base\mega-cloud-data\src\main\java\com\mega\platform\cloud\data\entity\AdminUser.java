package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "admin_user")
public class AdminUser {
    /**
     * 管理员ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 管理员用户名
     */
    @Column(name = "username")
    private String username;

    /**
     * bcrypt加密后的密码哈希值
     */
    @Column(name = "password")
    private String password;

    /**
     * 飞书uid
     */
    @Column(name = "feishu_uid")
    private String feishuUid;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private Date lastLoginTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识: 0=未删除, 1=已删除
     */
    @Column(name = "delsign")
    private Boolean delsign;
}