package com.mega.platform.cloud.core.exception;

import com.mega.platform.cloud.core.ResultCode;
import lombok.Getter;

@Getter
public class BaseException extends RuntimeException {

    private final Integer code;

    public BaseException(ResultCode code) {
        super(code.getMessage());
        this.code = code.getCode();
    }

    public BaseException(Integer code, String message) {
        super(message);
        this.code = code;
    }
}
