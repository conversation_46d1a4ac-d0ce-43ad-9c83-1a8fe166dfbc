package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("服务组查询请求参数")
public class AdminServicesGroupQueryReqVO {
    @ApiModelProperty(value = "projectAppId")
    private Long projectAppId;

    @ApiModelProperty(value = "服务组名称或备注（模糊查询）")
    private String keyword;

    @ApiModelProperty(value = "管理员ID")
    private Long adminUserId;

    @ApiModelProperty(value = "是否自研，1=自研，3=三方")
    private Integer isSelf;

    @ApiModelProperty(value = "组标签ID列表", example = "[1,2,3]")
    private List<Long> tagIds;

    @ApiModelProperty(value = "上线状态，0=下线，1=上线", example = "1")
    private Integer onlineStatus;

    @ApiModelProperty(value = "运行状态，0=停止，1=运行中", example = "1")
    private Integer runStatus;

    @ApiModelProperty(value = "真实运行状态，0=异常，1=正常", example = "1")
    private Integer realRunStatus;
}
