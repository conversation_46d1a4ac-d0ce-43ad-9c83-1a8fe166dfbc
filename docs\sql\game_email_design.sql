-- --------------------------------------------------------
-- Table structure for 3 通讯与通知 (Communication & Notification)
-- --------------------------------------------------------

CREATE TABLE `email` (
  `id` BIGINT  PRIMARY KEY AUTO_INCREMENT COMMENT '邮件ID',
  `send_character_id` BIGINT  NOT NULL DEFAULT 0 COMMENT '发送者账号ID(0表示系统发送)',
  `receive_character_id` BIGINT  NOT NULL DEFAULT 0 COMMENT '接收者ID(0表示全服邮件)',
  `title` VARCHAR(255) NOT NULL COMMENT '邮件标题',
  `content` TEXT NOT NULL COMMENT '邮件内容(支持富文本)',
  `rewards` JSON NOT NULL DEFAULT '{}' COMMENT '邮件奖励(JSON格式,包含物品id和数量)',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标志(0:未删除,1:已删除)',
  KEY `idx_send_character` (`send_character_id`),
  KEY `idx_receive_character` (`receive_character_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件表';

CREATE TABLE `email_read` (
  `character_id` BIGINT  NOT NULL COMMENT '账号ID',
  `email_id` BIGINT  NOT NULL COMMENT '邮件ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  PRIMARY KEY (`character_id`, `email_id`),
  KEY `idx_email_id` (`email_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件阅读记录表';
