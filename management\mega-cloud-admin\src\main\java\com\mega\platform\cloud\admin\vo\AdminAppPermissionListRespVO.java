package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("App权限列表响应数据")
public class AdminAppPermissionListRespVO {
    
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "所属App的ID", example = "1")
    private Long projectAppId;

    @ApiModelProperty(value = "所属App的名称", example = "auth")
    private String projectAppName;

    @ApiModelProperty(value = "Ant风格路径匹配", example = "/api/v1/user/**")
    private String urlPattern;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除：0=未删除，1=已删除", example = "0")
    private Boolean delsign;
}
