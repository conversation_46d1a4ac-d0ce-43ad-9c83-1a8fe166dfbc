package com.mega.platform.cloud.data.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("支付宝支付平台配置")
public class PaymentAlipayConfig {

    @ApiModelProperty("支付宝应用 AppID")
    private String appId;

    @ApiModelProperty("支付宝公钥")
    private String aliPayPublicKey;

    @ApiModelProperty("商户私钥")
    private String privateKey;

    @ApiModelProperty("支付成功回调地址")
    private String callbackUrl;
}
