package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "project_url_pattern")
public class ProjectUrlPattern {
    /**
     * AppID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Integer id;

    /**
     * 路由名称 例：auth路由、access路由
     */
    @Column(name = "name")
    private String name;

    /**
     * Ant风格路径匹配，例如 /api/v1/user/**
     */
    @Column(name = "url_pattern")
    private String urlPattern;

    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}