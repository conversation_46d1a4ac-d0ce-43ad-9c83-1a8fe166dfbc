package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_task")
public class JenkinsTask {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 任务组id
     */
    @Column(name = "jenkins_task_group_id")
    private Long jenkinsTaskGroupId;

    /**
     * Jenkins Job ID，关联jenkins_job(id)
     */
    @Column(name = "jenkins_job_id")
    private Long jenkinsJobId;

    /**
     * 操作类型（build、restart、stop、getlog、update等）
     */
    @Column(name = "action")
    private Integer action;

    /**
     * 操作参数或请求详情（如JSON）
     */
    @Column(name = "request_data")
    private String requestData;

    /**
     * 0-失败 1=成功
     */
    @Column(name = "is_success")
    private Byte isSuccess;

    /**
     * jenkins链接
     */
    @Column(name = "jenkins_job_url")
    private String jenkinsJobUrl;

    /**
     * gitcommit号
     */
    @Column(name = "git_commit")
    private String gitCommit;

    /**
     * 执行完成时间
     */
    @Column(name = "complete_time")
    private Date completeTime;

    /**
     * 失败原因
     */
    @Column(name = "failed_reason")
    private String failedReason;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}