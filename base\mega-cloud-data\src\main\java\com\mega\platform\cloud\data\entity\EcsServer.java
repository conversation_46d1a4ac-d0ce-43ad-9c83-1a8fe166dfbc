package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "ecs_server")
public class EcsServer {
    /**
     * 服务器id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 产品ID
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * appId
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 服务器名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 云主机实例ID/唯一标识
     */
    @Column(name = "instance_id")
    private String instanceId;

    /**
     * 创建ecs的配置表
     */
    @Column(name = "ecs_server_config_id")
    private Long ecsServerConfigId;

    /**
     * 镜像配置ID
     */
    @Column(name = "ecs_server_image_id")
    private Long ecsServerImageId;

    @Column(name = "public_ip")
    private String publicIp;

    @Column(name = "private_ip")
    private String privateIp;

    @Column(name = "ecs_server_system_id")
    private Long ecsServerSystemId;

    /**
     * 状态 0.异常 1.正常 2.正在创建 3.正在初始化 4.待关闭 5.已退 6.待升级 7.升级中
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}