# mega-cloud-admin 查询ServiceGroup的需求

AdminServiceManageController新增如下路由：

## 1 admin/api/{projectId}/microservice/service/list

全部services列表查询

#### 入参
@PathVariable("projectId") Long projectId 以及AdminServicesListReqVO。

AdminServicesListReqVO字段：
@ApiModelProperty("服务组ID，可选的，不为空拼接到sql")
private Long servicesGroupId;

@ApiModelProperty("服务名称，可选的，不为空拼接到sql, 模糊查询")
private String servicesName;

@ApiModelProperty("备注，可选的，不为空拼接到sql, 模糊查询")
private String remark;

@ApiModelProperty("管理员，可选的，不为空拼接到sql")
private Long adminUserId;

@ApiModelProperty("运行状态，可选的，不为空拼接到sql")
private Integer runningStatus;

@ApiModelProperty("组标签选择（多选）")
private List<Long> tags;

@ApiModelProperty("上线状态, 0：下线 1：上线 可选的，不为空拼接到sql") 
private Integer status;

@ApiModelProperty("运行状态, 0未运行
1运行中
2构建中
3队列中
4待关闭
-1构建失败，不为空拼接到sql") 
private Integer runningStatus;

真实运行状态：0未运行 1运行中 2构建中 3队列中 4待关闭 -1构建失败
private Integer realRunningStatus;


#### 出参
AdminServicesListRespVO参数：

services基本信息
组的基本信息
组标签
最后一次jenkins_task_group信息
 最后一次jenkins_task信息

#### Service
建立AdminServiceQueryService和AdminServiceQueryDao以及对应的mybaits xml


## 2 /admin/api/{projectId}/microservice/group/service/list

基于组的services查询

#### 入参
@PathVariable("projectId") Long projectId 以及AdminServicesListByGroupReqVO。

AdminServicesListByGroupReqVO字段：
* servicesGroupId必填

#### 出参
AdminServicesListByGroupRespVO。字段：
* services基本信息
* 最后一次jenkins_task信息

#### Service
在AdminServiceQueryService和AdminServiceQueryDao以及对应的mybaits xml


## 涉及的表结构

```sql
CREATE TABLE `jenkins_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `jenkins_task_group_id` bigint(20) NOT NULL COMMENT '任务组id',
  `jenkins_job_id` bigint(20) NOT NULL COMMENT 'Jenkins Job ID，关联jenkins_job(id)',
  `action` int(11) NOT NULL COMMENT '操作类型（build、restart、stop、getlog、update等）',
  `request_data` json DEFAULT NULL COMMENT '操作参数或请求详情（如JSON）',
  `is_success` tinyint(4) DEFAULT NULL COMMENT '0-失败 1=成功',
  `jenkins_job_url` varchar(255) DEFAULT NULL COMMENT 'jenkins链接',
  `git_commit` varchar(255) DEFAULT NULL COMMENT 'gitcommit号',
  `complete_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行完成时间',
  `failed_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) DEFAULT '0' COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fk_jenkins_task_job_id` (`jenkins_job_id`),
  KEY `fk_jenkins_task_group_id` (`jenkins_task_group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COMMENT='Jenkins Job操作任务记录表';
```

```sql
CREATE TABLE `jenkins_task_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `admin_user_id` bigint(20) NOT NULL COMMENT '操作用户ID，关联admin_user(id)',
  `services_group_id` bigint(20) NOT NULL COMMENT '微服务组id',
  `action` int(11) NOT NULL COMMENT '操作类型（build、restart、stop、getlog、update等）',
  `request_data` json DEFAULT NULL COMMENT '操作参数或请求详情（如JSON）',
  `task_num` int(11) DEFAULT NULL COMMENT '任务数量',
  `is_success` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-失败 1=成功',
  `failed_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `complete_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行完成时间',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delsign` tinyint(4) DEFAULT '0' COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  KEY `fk_services_group_id` (`services_group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COMMENT='Jenkins Job操作任务记录表';

```

