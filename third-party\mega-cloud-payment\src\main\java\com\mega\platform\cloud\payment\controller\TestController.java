package com.mega.platform.cloud.payment.controller;

import com.mega.platform.cloud.data.vo.TestReqVO;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.payment.service.TestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "支付接口")
@Slf4j
@RestController
public class TestController {

    private final TestService testService;

    @Autowired
    public TestController(TestService testService) {
        this.testService = testService;
    }

    @ApiOperation("mysql test")
    @PostMapping("/mysql/test")
    public Result<?> mysqlTest(@Validated @RequestBody TestReqVO vo) throws Exception {
        testService.mysqlTest(vo);
        return Results.success();
    }

    @ApiOperation("mongo test")
    @PostMapping("/mongo/test")
    public Result<?> mongoTest(@Validated @RequestBody TestReqVO vo) throws Exception {
        testService.mongoTest(vo);
        return Results.success();
    }

    @ApiOperation("redis test")
    @PostMapping("/redis/test")
    public Result<?> redisTest(@Validated @RequestBody TestReqVO vo) throws Exception {
        testService.redisTest(vo);
        return Results.success();
    }

    @ApiOperation("kafka test")
    @PostMapping("/kafka/test")
    public Result<?> kafkaTest(@Validated @RequestBody TestReqVO vo) throws Exception {
        testService.kafkaTest(vo);
        return Results.success();
    }
}
