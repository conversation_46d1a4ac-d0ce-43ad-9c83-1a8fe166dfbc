package com.mega.platform.cloud.data.dto.common.mail;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class BaseMailDTO {

    private String title = "未定义title";
    private String content;
    private File attachment;
    private List<String> to = new ArrayList<>();

    public void addTo(String to) {
        this.to.add(to);
    }
}
