package com.mega.platform.cloud.client.microservice;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = "mega-cloud-microservice-" + "${spring.profiles.active}", contextId = "mega-cloud-microservice-services-client")
@Api(tags = "服务接口")
public interface ServicesClient {
    @ApiOperation("创建服务")
    @PostMapping("/microservice/api/services/create")
    public Result<CreateServicesRespVO> createServices(@Validated @RequestBody CreateServicesReqVO vo) throws Exception;

    @ApiOperation("构建服务")
    @PostMapping("/microservice/api/services/build")
    public Result<?> buildServices(@Validated @RequestBody BuildServicesReqVO vo) throws Exception;
}
