package com.mega.platform.cloud.common.utils;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.mega.platform.cloud.common.CommonConfig;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;

import static com.mega.platform.cloud.common.constant.MicroserviceConstants.SERVER_PASSWORD;
import static com.mega.platform.cloud.common.constant.MicroserviceConstants.SERVER_USERNAME;

@Service
@Slf4j
public class ServicesCheckUtils {
    /**
     * jsch 检查某个ip的端口是否开放
     *
     * @param serverIp
     * @param checkPort
     * @return
     */
    public static Boolean checkServiceIsRunning(String serverIp, Integer checkPort) {
        if (CommonConfig.ENV.equals("dev")) {
            return true;
        }
        return jschExecuteCommand(serverIp, checkPort, "ss -tulnp | grep :" + checkPort);
    }

    /**
     * 检查服务consul是否注册成功
     *
     * @param serverIp
     * @param checkPort
     * @throws Exception
     */
    public static Boolean checkServiceConsulUp(String serverIp, Integer checkPort) {
        if (CommonConfig.ENV.equals("dev")) {
            return true;
        }
        // 查询 Consul 健康状态
        try {
            String url = String.format("http://%s:%s/actuator/health", serverIp, checkPort);
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder().uri(new URI(url)).timeout(Duration.ofSeconds(3)).GET().build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            int statusCode = response.statusCode();
            String body = response.body();
            ConsulStatus consulStatus = JsonUtils.fromJson(body, ConsulStatus.class);
            if (statusCode == 200 && (consulStatus.getStatus().equals("UP"))) {
                log.info("checkServiceConsulUp consul status up.");
                return true;
            } else {
                log.info("checkServiceConsulUp consul not ready. code: {}, status: {}, url: {}", statusCode, consulStatus.getStatus(), url);
                return false;
            }
        } catch (Exception e) {
            log.warn("checkServiceConsulUp occur exception. reason: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通知服务需要即将关闭
     *
     * @param serverIp
     * @param checkPort
     * @return
     * @throws Exception
     */
    public static Boolean notifyServicesStop(String serverIp, Integer checkPort) throws Exception {
        if (CommonConfig.ENV.equals("dev")) {
            return true;
        }
        try {
            String url = String.format("http://%s:%s/actuator/platform/notify/stop", serverIp, checkPort);
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder().uri(new URI(url)).timeout(Duration.ofSeconds(3)).POST(HttpRequest.BodyPublishers.noBody()).build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            int statusCode = response.statusCode();
            String body = response.body();
            Result result = JsonUtils.fromJson(body, Result.class);
            if (statusCode == 200 && (result.getCode().equals(1))) {
                log.info("notifyServicesStop success.");
                return true;
            } else {
                log.info("notifyServicesStop failed. code: {}, status: {}, url: {}", statusCode, result.getMessage(), url);
                return false;
            }
        } catch (Exception e) {
            log.warn("notifyServicesStop occur exception. reason: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查服务是否处于可关闭状态
     *
     * @param serverIp
     * @param checkPort
     * @return
     * @throws Exception
     */
    public static Boolean checkServicesCanStop(String serverIp, Integer checkPort) throws Exception {
        if (CommonConfig.ENV.equals("dev")) {
            return true;
        }
        try {
            String url = String.format("http://%s:%s/actuator/platform/check/canStop", serverIp, checkPort);
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder().uri(new URI(url)).timeout(Duration.ofSeconds(3)).POST(HttpRequest.BodyPublishers.noBody()).build();
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            int statusCode = response.statusCode();
            String body = response.body();
            Result result = JsonUtils.fromJson(body, Result.class);
            if (statusCode == 200 && (result.getCode().equals(0))) {
                log.info("checkServicesCanStop success.");
                return true;
            } else {
                log.info("checkServicesCanStop failed. code: {}, status: {}, url: {}", statusCode, result.getMessage(), url);
                return false;
            }
        } catch (Exception e) {
            log.warn("checkServicesCanStop occur exception. reason: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 执行自定义脚本检查返回内容
     *
     * @param serverIp
     * @param checkPort
     * @return
     */
    public static Boolean checkServiceCustomScript(String serverIp, Integer checkPort, String customScript) {
        if (CommonConfig.ENV.equals("dev")) {
            return true;
        }
        return jschExecuteCommand(serverIp, checkPort, customScript);
    }

    private static Boolean jschExecuteCommand(String serverIp, Integer checkPort, String command) {
        Session session = null;
        ChannelExec channel = null;
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(SERVER_USERNAME, serverIp, 22); // ssh开放端口默认22
            session.setPassword(SERVER_PASSWORD);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect(3000); // 3秒超时
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);
            channel.setInputStream(null);
            InputStream in = channel.getInputStream();
            channel.connect();
            String result = new String(in.readAllBytes(), StandardCharsets.UTF_8);
            Boolean isRunning = !result.trim().isEmpty();
            log.info("checkServiceIsRunning service {}:{} isRunning: {} result: {}", serverIp, checkPort, isRunning, result);
            return isRunning;
        } catch (Exception e) {
            log.error("checkServiceIsRunning occur exception", e);
            return false;
        } finally {
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    @Data
    private static class ConsulStatus {
        private String status;
    }

    public static void main(String[] args) throws Exception {
        checkServiceConsulUp("**************", 8973);
    }
}