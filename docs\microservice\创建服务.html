<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建服务</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        h2 {
            text-align: center;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .form-group {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<div class="container">
    <h2>创建服务</h2>
    <form id="createServiceForm">
        <div class="form-group">
            <label for="serviceName">服务名</label>
            <input type="text" id="serviceName" name="serviceName" required placeholder="请输入服务名称">
        </div>

        <div class="form-group">
            <label for="param1">参数 param1</label>
            <input type="text" id="param1" name="param1" required placeholder="请输入参数 param1" value="value1">
        </div>

        <div class="form-group">
            <label for="param2">参数 param2</label>
            <input type="text" id="param2" name="param2" required placeholder="请输入参数 param2">
        </div>

        <div class="form-group">
            <label for="param3">参数 param3</label>
            <input type="text" id="param3" name="param3" required placeholder="请输入参数 param3">
        </div>

        <button type="submit">创建服务</button>
    </form>
</div>

<script>
    document.getElementById('createServiceForm').addEventListener('submit', function(event) {
        event.preventDefault(); // 防止默认表单提交

        // 获取表单中的数据
        const serviceName = document.getElementById('serviceName').value;
        const param1 = document.getElementById('param1').value;
        const param2 = document.getElementById('param2').value;
        const param3 = document.getElementById('param3').value;

        // 显示提交的内容
        alert(`服务名称: ${serviceName}\nparam1: ${param1}\nparam2: ${param2}\nparam3: ${param3}`);

        // 可以在这里向后台发送请求创建服务
        // 示例：通过 Fetch API 发送数据
        /*
        fetch('/api/createService', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                serviceName: serviceName,
                param1: param1,
                param2: param2,
                param3: param3
            })
        }).then(response => response.json())
          .then(data => {
            console.log('Service created:', data);
          })
          .catch((error) => {
            console.error('Error:', error);
          });
        */
    });
</script>
</body>
</html>
