package com.mega.platform.cloud.common.utils;

import com.mega.platform.cloud.common.config.JwtProperties;
import com.mega.platform.cloud.common.constant.JwtConstants;
import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
@Component
public class JwtTokenUtil {
    private final JwtProperties jwtProperties;

    public JwtTokenUtil(JwtProperties jwtProperties) {
        this.jwtProperties = jwtProperties;
    }

    public String generateToken(BaseReqVO baseReqVO) {
        return Jwts.builder()
                .claim(JwtConstants.CLAIM_APP_ID, baseReqVO.getAppId())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtProperties.getExpireSeconds() * 1000))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    private Key getSigningKey() {
        byte[] keyBytes = jwtProperties.getSecret().getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }


    public BaseReqVO parseToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        BaseReqVO baseReqVO = new BaseReqVO();
        baseReqVO.setAppId(claims.get(JwtConstants.CLAIM_APP_ID, Long.class));
        return baseReqVO;
    }

    public Integer getAppId(HttpServletRequest request) {
       return (Integer) request.getAttribute(JwtConstants.CLAIM_APP_ID);
    }
}
