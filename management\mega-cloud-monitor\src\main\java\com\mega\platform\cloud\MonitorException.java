package com.mega.platform.cloud;


import com.mega.platform.cloud.core.exception.BaseException;

import java.util.Objects;

public class MonitorException extends BaseException {

    public MonitorException(Integer code) {
        this(Objects.requireNonNull(MonitorErrorCode.getExchangeCode(code)));
    }

    public MonitorException(MonitorErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public MonitorException(Integer code, String message) {
        super(code, message);
    }
}
