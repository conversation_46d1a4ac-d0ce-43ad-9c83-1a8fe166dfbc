package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.vo.AdminProjectCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminProjectListReqVO;
import com.mega.platform.cloud.admin.vo.AdminProjectEditReqVO;
import com.mega.platform.cloud.admin.service.AdminProjectService;
import com.mega.platform.cloud.admin.vo.AdminProjectVO;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 项目管理控制器
 */
@RestController
@RequestMapping("/admin/api")
@Api(tags = "项目管理")
@Slf4j
public class AdminProjectController {

    private final AdminProjectService adminProjectService;

    @Autowired
    public AdminProjectController(AdminProjectService adminProjectService) {
        this.adminProjectService = adminProjectService;
    }

    /**
     * 项目列表查询
     * 
     * @param reqVO 项目列表查询参数
     * @return 项目列表
     */
    @PostMapping("/system/project/list")
    @ApiOperation("项目列表查询")
    public Result<PageResult<AdminProjectVO>> list(@Valid @RequestBody AdminProjectListReqVO reqVO) {
        PageResult<AdminProjectVO> result = adminProjectService.findProjectList(reqVO);
        return Results.success(result);
    }

    /**
     * 项目详情查询
     * 
     * @param projectId 项目ID
     * @return 项目详情
     */
    @PostMapping("/{projectId}/project/detail")
    @ApiOperation("项目详情查询")
    public Result<AdminProjectVO> detail(@PathVariable("projectId") Long projectId) {
        AdminProjectVO result = adminProjectService.getProjectById(projectId);
        return Results.success(result);
    }

    /**
     * 创建项目
     * 
     * @param createPO 创建参数
     * @return 项目ID
     */
    @PostMapping("/system/project/create")
    @ApiOperation("项目创建")
    public Result<Long> create(@Valid @RequestBody AdminProjectCreateReqVO createPO) {
        Long projectId = adminProjectService.createProject(createPO);
        return Results.success(projectId);
    }

    /**
     * 编辑项目
     * 
     * @param projectId 项目ID
     * @param updatePO  更新参数
     * @return 操作结果
     */
    @PostMapping("/{projectId}/project/edit")
    @ApiOperation("项目编辑")
    public Result<?> edit(@PathVariable("projectId") Long projectId,
            @Valid @RequestBody AdminProjectEditReqVO updatePO) {
        adminProjectService.editProject(projectId, updatePO);
        return Results.success();
    }

    /**
     * 删除项目
     * 
     * @param projectId 项目ID
     * @return 操作结果
     */
    @PostMapping("/{projectId}/project/delete")
    @ApiOperation("项目删除")
    public Result<?> delete(@PathVariable("projectId") Long projectId) {
        adminProjectService.deleteProject(projectId);
        return Results.success();
    }
}