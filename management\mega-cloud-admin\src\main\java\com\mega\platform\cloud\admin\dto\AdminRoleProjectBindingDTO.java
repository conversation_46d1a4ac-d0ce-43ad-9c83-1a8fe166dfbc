package com.mega.platform.cloud.admin.dto;

import javax.persistence.Column;

import lombok.Data;
import lombok.experimental.Accessors;



@Data
@Accessors(chain = true)
public class AdminRoleProjectBindingDTO {
    /**
     * 角色ID
     */
    @Column(name = "admin_role_id")
    private Long adminRoleId;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * 项目名称
     */
    @Column(name = "project_name")
    private String projectName;
      /**
     * 删除标识: 0=未删除, 1=已删除
     */
    @Column(name = "delsign")
    private Boolean delsign;

}
