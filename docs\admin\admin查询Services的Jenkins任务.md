# mega-cloud-admin 查询 Services 的 Jenkins 任务的需求

在 AdminServiceManageController 新增如下路由：

## 1. /admin/api/{projectId}/microservice/job/last

查询services最后一次任务

#### 入参

@PathVariable("projectId") Long projectId 以及 AdminServicesLastTaskReqVO。

AdminServicesLastTaskReqVO 字段：
@ApiModelProperty("服务 ID")
private Long servicesId;

#### 出参

AdminServicesLastTaskRespVO。字段：
参考sql语句的查询结构


#### Service

在 AdminServiceQueryService 和 AdminServiceQueryDao 以及对应的 mybaits xml

sql 语句如下：

```sql

-- 查询当前service最后一次task
SELECT 
    s.id                  AS servicesId,
    s.name                AS servicesName,
    s.remark              AS servicesRemark,
    s.status              AS servicesStatus,
    s.running_status      AS servicesRunningStatus,
    s.real_running_status AS servicesRealRunningStatus,
    s.create_time         AS serviceId,
    lt.id                 AS lastTaskId,
    lt.action             AS lastTaskAction,
    lt.is_success         AS lastTaskIsSuccess,
    lt.failed_reason      AS lastFailedReason,
    lt.git_commit         AS lastGitCommit,
    lt.jenkins_job_id     AS lastJenkinsJobId,
    lt.jenkins_job_url,   AS lastJenkinsJobUrl,
    lt.jenkins_task_group_id, AS lastJenkinsTaskGroupId,
    lt.request_data,          AS lastTaskRequestData,  
    lt.remark             AS lastTaskRemark,
    lt.complete_time      AS lastTaskCompleteTime,
    lt.jenkins_job_url    AS lastTaskJenkinsJobUrl
FROM services AS s
LEFT JOIN services_group AS sg ON sg.id = s.services_group_id
LEFT JOIN (
    SELECT jenkins_job_id, MAX(id) AS max_id
    FROM jenkins_task
    WHERE delsign = 0
    GROUP BY jenkins_job_id
) lt_max ON s.jenkins_job_id = lt_max.jenkins_job_id
LEFT JOIN jenkins_task lt ON lt_max.max_id = lt.id
WHERE 
s.delsign = 0
AND sg.project_id = 1
AND sg.delsign = 0
AND lt.delsign = 0
```

## 2. /admin/api/{projectId}/microservice/job/group/list
查询任务组列表

#### 入参

@PathVariable("projectId") Long projectId 以及 AdminJenkinsTaskGroupListReqVO。

AdminJenkinsTaskGroupListReqVO 字段：
@ApiModelProperty("服务组 ID")
private Long servicesGroupId;

#### 出参

AdminJenkinsTaskGroupListRespVO。字段：
参考sql语句的查询结构

#### Service

在 AdminServiceQueryService 和 AdminServiceQueryDao 以及对应的 mybaits xml

sql 语句如下：

```sql
-- 查询任务组列表
SELECT 
jtg.*
FROM jenkins_task_group AS jtg
LEFT JOIN services_group AS sg ON jtg.services_group_id = sg.id
WHERE sg.project_id = 1
AND jtg.delsign = 0 
AND sg.delsign = 0
ORDER BY jtg.id DESC
```

## 3. /admin/api/{projectId}/microservice/job/group/log
查询任务日志

#### 入参   

@PathVariable("projectId") Long projectId 以及 AdminJenkinsTaskGroupLogReqVO。

AdminJenkinsTaskGroupLogReqVO 字段：
@ApiModelProperty("任务组 ID")
private Long jenkinsTaskGroupId;

#### 出参

AdminJenkinsTaskGroupLogRespVO。字段：
* jenkinsTaskGroupId bigint
* jenkinsTaskId bigint 
* logContentList List<String>

#### Service

在 AdminServiceQueryService 和 AdminServiceQueryDao 以及对应的 mybaits xml

sql 语句如下：

```sql
-- 查询任务日志
SELECT 
jtl.jenkins_task_group_id，
jtl.jenkins_task_id,
jtl.log_time,
jtl.log_content
FROM jenkins_task_log AS jtl
LEFT JOIN jenkins_task_group AS jtg ON jtg.id = jtl.jenkins_task_group_id
LEFT JOIN services_group AS sg ON jtg.services_group_id = sg.id
WHERE 
jtl.jenkins_task_group_id = 27
AND sg.project_id = 1
```

查询结果把相同的jenkins_task_id，把log_content放到一个List<String>中。