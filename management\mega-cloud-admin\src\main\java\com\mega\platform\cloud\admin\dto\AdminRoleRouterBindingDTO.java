package com.mega.platform.cloud.admin.dto;

import javax.persistence.Column;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AdminRoleRouterBindingDTO {
     /**
     * 角色ID
     */
    @Column(name = "admin_role_id")
    private Long adminRoleId;

    /**
     * 路由ID
     */
    @Column(name = "admin_router_id")
    private Long adminRouterId;

    /**
     * 路由名称
     */
    @Column(name = "router_name")
    private String routerName;
    
    /**
     * 删除标识: 0=未删除, 1=已删除
     */
    @Column(name = "delsign")
    private Boolean delsign;
}
