package com.mega.platform.cloud.monitor.service.metrics;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.mega.platform.cloud.common.constant.MonitorConstants.*;

@Service
@Slf4j
public class MetricsCommonService {
    @Value("${monitor.topic.metrics-ecs-collect}")
    private String ecsTopic;
    @Value("${monitor.topic.metrics-services-collect}")
    private String servicesTopic;
    @Value("${monitor.collection.metrics-ecs-server-data}")
    private String ecsServerDataCollection;
    @Value("${monitor.collection.metrics-ecs-biz-data}")
    private String ecsBizDataCollection;
    @Value("${monitor.collection.metrics-services-server-data}")
    private String servicesServerDataCollection;
    @Value("${monitor.collection.metrics-services-biz-data}")
    private String servicesBizDataCollection;

    /**
     * 获取kafka队列名
     * @param sourceType
     * @return
     */
    public String getMetricsCollectTopic(Integer sourceType) {
        if (sourceType.equals(METRICS_SOURCE_TYPE_ECS)) {
            return ecsTopic;
        }
        else if (sourceType.equals(METRICS_SOURCE_TYPE_SERVICES)) {
            return servicesTopic;
        }
        return null;
    }


    /**
     * 获取具体mongo表名
     * @param sourceType
     * @param metricsType
     * @return
     */
    public String getMetricsDataCollectionName(Integer sourceType, Integer metricsType) {
        if (sourceType.equals(METRICS_SOURCE_TYPE_ECS) && metricsType.equals(METRICS_TYPE_SERVER)) {
            return ecsServerDataCollection;
        } else if (sourceType.equals(METRICS_SOURCE_TYPE_ECS) && metricsType.equals(METRICS_TYPE_BIZ)) {
            return ecsBizDataCollection;
        } else if (sourceType.equals(METRICS_SOURCE_TYPE_SERVICES) && metricsType.equals(METRICS_TYPE_SERVER)) {
            return servicesServerDataCollection;
        } else if (sourceType.equals(METRICS_SOURCE_TYPE_SERVICES) && metricsType.equals(METRICS_TYPE_BIZ)) {
            return servicesBizDataCollection;
        }
        return null;
    }
}
