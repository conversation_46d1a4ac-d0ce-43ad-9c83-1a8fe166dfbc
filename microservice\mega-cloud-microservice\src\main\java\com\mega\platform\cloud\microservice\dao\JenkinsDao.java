package com.mega.platform.cloud.microservice.dao;

import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.entity.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface JenkinsDao {
    void insertJenkinsSshServer(@Param("jenkinsSshServer") JenkinsSshServer jenkinsSshServer);

    void insertJenkinsView(@Param("jenkinsView") JenkinsView jenkinsView);

    List<JenkinsTemplateParamDTO> getTemplateParamKeyValueListByDataType(@Param("jenkinsTemplateId") Long jenkinsTemplateId, @Param("servicesDataType") Integer servicesDataType, @Param("servicesDataId") Long servicesDataId);

    List<JenkinsTemplateParamDTO> getTemplateParamKeyListByDataType(@Param("jenkinsTemplateId") Long jenkinsTemplateId, @Param("servicesDataType") Integer servicesDataType);

    JenkinsJob getSameEcsServerJenkinsJob(@Param("serviceGroupId") Long serviceGroupId, @Param("ecsServerId") Long ecsServerId, @Param("servicesId") Long servicesId);

    void insertJenkinsJob(@Param("jenkinsJob") JenkinsJob jenkinsJob);

    String getServicesDataJenkinsParamValue(@Param("servicesDataId") Long servicesDataId, @Param("servicesDataType") Integer servicesDataType, @Param("param") String param);

    List<JenkinsTemplateParamDTO> getServicesDataJenkinsParamValues(@Param("servicesDataId") Long servicesDataId, @Param("servicesDataType") Integer servicesDataType);

    void insertJenkinsTaskGroup(@Param("taskGroup") JenkinsTaskGroup taskGroup);

    void insertJenkinsTask(@Param("task") JenkinsTask task);

    void insertJenkinsUser(@Param("jenkinsUser") JenkinsUser jenkinsUser);

    JenkinsTaskGroup getLastJenkinsTaskGroupByServicesGroupId(@Param("servicesGroupId") Long servicesGroupId);
}