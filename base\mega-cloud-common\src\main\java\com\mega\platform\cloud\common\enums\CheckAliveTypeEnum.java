package com.mega.platform.cloud.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CheckAliveTypeEnum {
    PORT_CONSUL(1, true, true, false),
    PORT(2, true, false, false),
    CONSUL(3, false, true, false),
    CUSTOM(4, false, false, true),
    CUSTOM_CONSUL(5, false, true, true),
    NO_CHECK(0, false, false, false),
    ;

    private final Integer checkType;
    private final Boolean checkPort;
    private final Boolean checkConsul;
    private final Boolean checkCustom;

    public static CheckAliveTypeEnum findByType(Integer type) {
        for (CheckAliveTypeEnum typeEnum : CheckAliveTypeEnum.values()) {
            if (typeEnum.getCheckType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
