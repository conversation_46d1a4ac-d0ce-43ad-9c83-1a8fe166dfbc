package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.EcsServer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("Ecs Server列表响应数据")
public class AdminBaseEcsServerListRespVO {
    @ApiModelProperty("Ecs Server列表")
    private List<EcsServer> ecsServerList;
}
