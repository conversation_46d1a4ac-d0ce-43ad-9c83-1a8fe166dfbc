package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-管理员项目绑定列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员项目绑定列表响应")
public class AdminAccessUserProjectBindingListRespVO {

    @ApiModelProperty("管理员ID")
    private Long adminUserId;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("绑定状态: 0绑定, 1=解绑")
    private Integer delsign;
}