package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("路由配置列表响应数据")
public class AdminUrlPatternListRespVO {
    
    @ApiModelProperty(value = "AppID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "路由名称", example = "auth路由")
    private String name;

    @ApiModelProperty(value = "Ant风格路径匹配", example = "/api/v1/user/**")
    private String urlPattern;

    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识(0=未删除,1=已删除)", example = "0")
    private Integer delsign;
}
