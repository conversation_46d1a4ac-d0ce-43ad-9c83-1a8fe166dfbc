package com.mega.platform.cloud.admin.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Collections;

/**
 * Admin模组专用Swagger配置
 * 覆盖common模组的配置，使用adminToken作为认证头
 */
@Profile({"dev", "test"})
@EnableOpenApi
@Configuration
public class AdminSwaggerConfig {

    @Value("${api.docs.apis-package:com.mega.platform.cloud.admin.controller}")
    private String apisPackage;

    @Value("${api.docs.title:Mega Platform Admin}")
    private String title;

    @Value("${api.docs.version:1.0.1}")
    private String version;

    @Value("${api.docs.description:美嘉平台后台管理系统API文档}")
    private String description;

    /**
     * 创建Admin专用的Docket Bean，指定唯一的组名避免与common模组冲突
     */
    @Bean
    public Docket adminApi() {
        return new Docket(DocumentationType.OAS_30)
                .groupName("admin") // 指定唯一的组名
                .useDefaultResponseMessages(false)
                .apiInfo(new ApiInfoBuilder()
                        .title(title)
                        .version(version)
                        .description(description)
                        .contact(new Contact("Mega Platform Admin", "", ""))
                        .build())
                .select()
                .apis(RequestHandlerSelectors.basePackage(apisPackage))
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(Collections.singletonList(adminToken()))
                .securityContexts(Collections.singletonList(securityContext()));
    }

    /**
     * 配置adminToken认证方式
     * 使用ApiKey方式，header名称为adminToken
     */
    private ApiKey adminToken() {
        return new ApiKey("adminToken", "adminToken", "header");
    }

    /**
     * 配置安全上下文
     * 指定哪些接口需要使用adminToken认证
     */
    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(Collections.singletonList(
                        SecurityReference.builder()
                                .reference("adminToken")
                                .scopes(new AuthorizationScope[0])
                                .build()))
                .operationSelector(operationContext -> {
                    // 排除public接口，其他接口都需要认证
                    String path = operationContext.requestMappingPattern();
                    return path != null && !path.contains("/public/");
                })
                .build();
    }
}
