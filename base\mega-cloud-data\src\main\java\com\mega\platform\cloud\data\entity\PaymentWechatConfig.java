package com.mega.platform.cloud.data.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("微信支付平台配置")
public class PaymentWechatConfig {

    @ApiModelProperty("微信支付 API V3 密钥")
    private String apiV3Key;

    @ApiModelProperty("微信商户号")
    private String merchantId;

    @ApiModelProperty("商户私钥文件路径（.pem 文件）")
    private String privateKeyPath;

    @ApiModelProperty("微信平台证书序列号")
    private String merchantSerialNumber;

    @ApiModelProperty("微信开放平台申请的 AppID")
    private String appId;

    @ApiModelProperty("支付成功通知地址")
    private String noticeUrl;
}


