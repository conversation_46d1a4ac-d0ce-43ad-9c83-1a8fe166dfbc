package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.JenkinsJobTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("jenkins模板列表响应数据")
public class AdminBaseJenkinsTemplateListRespVO {
    @ApiModelProperty(value = "jenkins模板列表")
    private List<JenkinsJobTemplate> jenkinsJobTemplates;
}
