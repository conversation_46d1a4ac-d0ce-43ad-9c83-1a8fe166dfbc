package com.mega.platform.cloud.data.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "monitor_data")
public class MonitorData {
    /**
     * 采集数据ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 指标类型ID
     */
    @Column(name = "monitor_metric_id")
    private Long monitorMetricId;

    /**
     * 类型 1-ecs 2-services
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 目标ID（ecs_server_id 或 services_id）
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 实际采集值
     */
    @Column(name = "value")
    private BigDecimal value;

    /**
     * 采集时间
     */
    @Column(name = "collected_time")
    private Date collectedTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "is_deleted")
    private Byte isDeleted;
}