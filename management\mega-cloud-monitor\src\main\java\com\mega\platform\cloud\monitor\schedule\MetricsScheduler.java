package com.mega.platform.cloud.monitor.schedule;

import com.mega.platform.cloud.data.dto.monitor.MetricsAlarmDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsRuleDTO;
import com.mega.platform.cloud.monitor.dao.MetricsDao;
import com.mega.platform.cloud.monitor.service.metrics.MetricsRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class MetricsScheduler {

    private final MetricsRuleService metricsRuleService;
    private final MetricsDao metricsDao;

    @Autowired
    public MetricsScheduler(MetricsRuleService metricsRuleService, MetricsDao metricsDao) {
        this.metricsRuleService = metricsRuleService;
        this.metricsDao = metricsDao;
    }

    @Scheduled(fixedDelay = 10 * 1000)
    public void metricsRuleScan() {
        List<MetricsRuleDTO> rules = metricsDao.getMetricsRuleDTOScanList();
        for (MetricsRuleDTO rule : rules) {
            try {
                metricsRuleService.checkRule(rule);
            } catch (Exception e) {
                log.error("metricsRuleScan checkRule error, rule: {}", rule, e);
            }
        }
        log.info("metricsRuleScan success");
    }

    @Scheduled(fixedDelay = 10 * 1000)
    public void metricsAlarmScan() {
        List<MetricsAlarmDTO> alarms = metricsDao.getMetricsAlarmDTOScanList();
        for (MetricsAlarmDTO alarm : alarms) {
            try {
                metricsRuleService.checkAlarm(alarm);
            } catch (Exception e) {
                log.error("metricsRuleScan checkAlarm error, alarm: {}", alarm, e);
            }
        }
        log.info("metricsAlarmScan success");
    }

//    @Scheduled(fixedDelay = 10 * 1000)
//    public void servicesRunningStatusScan() {
//        List<MetricsAlarmDTO> alarms = metricsDao.getMetricsAlarmDTOScanList();
//        for (MetricsAlarmDTO alarm : alarms) {
//            try {
//                metricsRuleService.checkAlarm(alarm);
//            } catch (Exception e) {
//                log.error("servicesRunningStatusScan checkAlarm error, alarm: {}", alarm, e);
//            }
//        }
//        log.info("servicesRunningStatusScan success");
//    }
}
