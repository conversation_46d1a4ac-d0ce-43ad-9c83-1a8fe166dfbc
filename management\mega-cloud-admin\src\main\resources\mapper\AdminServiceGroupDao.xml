<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminServiceGroupDao">
        
    <!-- 基础字段 -->
    <sql id="BaseColumns">
        id, name, project_id, project_app_id, services_update_type, services_env, 
        services_log_format_id, services_alive_num, jenkins_services_id, jenkins_template_id, 
        is_self, admin_user_id, remark, status, running_status, real_running_status, 
        check_alive_type, use_jenkins, create_time, update_time, delsign
    </sql>
    
    <!-- 根据项目ID和服务组名查询服务组 -->
    <select id="selectServicesGroupByProjectIdAndName" resultType="com.mega.platform.cloud.data.entity.ServicesGroup">
        SELECT <include refid="BaseColumns"/>
        FROM services_group
        WHERE project_id = #{projectId} AND name = #{name} AND delsign = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>
    
    <!-- 根据ID查询服务组 -->
    <select id="selectServicesGroupById" resultType="com.mega.platform.cloud.data.entity.ServicesGroup">
        SELECT <include refid="BaseColumns"/>
        FROM services_group
        WHERE id = #{id} AND delsign = 0
    </select>
    
    <!-- 根据项目ID查询服务组列表 -->
    <select id="selectServicesGroupListByProjectId" resultType="com.mega.platform.cloud.data.entity.ServicesGroup">
        SELECT <include refid="BaseColumns"/>
        FROM services_group
        WHERE project_id = #{projectId} AND delsign = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 检查服务组下是否有运行中的服务 -->
    <select id="countRunningServicesByServicesGroupId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM services
        WHERE services_group_id = #{servicesGroupId}
          AND status = 1
          AND delsign = 0
          AND running_status = 1
    </select>
    
    <!-- 检查服务组下是否有服务 -->
    <select id="countServicesByServicesGroupId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM services
        WHERE services_group_id = #{servicesGroupId}
          AND delsign = 0
    </select>
    
    <!-- 更新服务组状态 -->
    <update id="updateServicesGroupStatus">
        UPDATE services_group
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id} AND delsign = 0
    </update>
    
    <!-- 逻辑删除服务组 -->
    <update id="deleteServicesGroupById">
        UPDATE services_group
        SET delsign = 1, update_time = NOW()
        WHERE id = #{id} AND delsign = 0
    </update>
    
    <!-- 更新服务组信息（编辑时使用） -->
    <update id="updateServicesGroupSelective">
        UPDATE services_group
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="servicesUpdateType != null">
                services_update_type = #{servicesUpdateType},
            </if>
            <if test="servicesEnv != null and servicesEnv != ''">
                services_env = #{servicesEnv},
            </if>
            <if test="servicesLogFormatId != null">
                services_log_format_id = #{servicesLogFormatId},
            </if>
            <if test="servicesAliveNum != null">
                services_alive_num = #{servicesAliveNum},
            </if>
            <if test="jenkinsServicesId != null">
                jenkins_services_id = #{jenkinsServicesId},
            </if>
            <if test="jenkinsTemplateId != null">
                jenkins_template_id = #{jenkinsTemplateId},
            </if>
            <if test="isSelf != null">
                is_self = #{isSelf},
            </if>
            <if test="adminUserId != null">
                admin_user_id = #{adminUserId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="checkAliveType != null">
                check_alive_type = #{checkAliveType},
            </if>
            <if test="useJenkins != null">
                use_jenkins = #{useJenkins},
            </if>
            
            update_time = NOW()
        </set>
        WHERE id = #{id} AND delsign = 0
    </update>

    <!-- insertServicesGroupTagRelation --> 
    <insert id="insertServicesGroupTagRelation">
        INSERT INTO services_group_tag_relation (service_group_id, service_tag_id, create_time, update_time, delsign)
        VALUES
        <foreach collection="tagRelations" item="relation" separator=",">
            (#{relation.serviceGroupId}, #{relation.serviceTagId}, #{relation.createTime}, #{relation.updateTime}, #{relation.delsign})
        </foreach>
        ON DUPLICATE KEY UPDATE 
            update_time = VALUES(update_time),
            delsign = VALUES(delsign)
    </insert>

    <!-- selectServicesGroupTagRelation --> 
    <select id="selectServicesGroupTagRelation" resultType="com.mega.platform.cloud.data.entity.ServicesGroupTagRelation">
        SELECT id, service_group_id, service_tag_id, create_time, update_time, delsign
        FROM services_group_tag_relation
        WHERE service_group_id = #{servicesGroupId} AND delsign = 0
    </select>

    <select id="selectServicesGroups" resultType="com.mega.platform.cloud.admin.vo.AdminServicesGroupRespVO">
        SELECT
        g.id AS servicesGroupId,
        g.name AS servicesGroupName,
        g.remark,
        g.admin_user_id AS adminUserId,
        g.is_self AS isSelf,
        g.status AS onlineStatus,
        g.running_status AS runStatus,
        g.real_running_status AS realRunStatus,
        GROUP_CONCAT(r.service_tag_id) AS tagIds
        FROM services_group g
        LEFT JOIN services_group_tag_relation r
        ON g.id = r.service_group_id AND r.delsign = 0
        WHERE g.delsign = 0
        <if test="projectId != null">
            AND g.project_id = #{projectId}
        </if>
        <if test="reqVO.projectAppId != null">
            AND g.project_app_id = #{reqVO.projectAppId}
        </if>
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            AND (g.name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR g.remark LIKE CONCAT('%', #{reqVO.keyword}, '%'))
        </if>
        <if test="reqVO.adminUserId != null">
            AND g.admin_user_id = #{reqVO.adminUserId}
        </if>
        <if test="reqVO.isSelf != null">
            AND g.is_self = #{reqVO.isSelf}
        </if>
        <if test="reqVO.onlineStatus != null">
            AND g.status = #{reqVO.onlineStatus}
        </if>
        <if test="reqVO.runStatus != null">
            AND g.running_status = #{reqVO.runStatus}
        </if>
        <if test="reqVO.realRunStatus != null">
            AND g.real_running_status = #{reqVO.realRunStatus}
        </if>
        <if test="reqVO.tagIds != null and reqVO.tagIds.size > 0">
            AND EXISTS (
            SELECT 1 FROM services_group_tag_relation r2
            WHERE r2.service_group_id = g.id
            AND r2.delsign = 0
            AND r2.service_tag_id IN
            <foreach collection="reqVO.tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
            )
        </if>
        ORDER BY g.id DESC
    </select>

    <!-- updateJenkinsTemplateParams -->
    <insert id="updateJenkinsTemplateParams">
        INSERT INTO jenkins_job_template_param_value
        (jenkins_job_templete_param_id, services_data_id, services_data_type,
        param_value, create_time, update_time, delsign)
        VALUES
        <foreach collection="jenkinsParams" item="param" separator=",">
            (#{param.jenkinsJobTempleteParamId}, #{servicesGroupId}, 1,
            #{param.paramValue},NOW(), NOW(), 0)
        </foreach>
        ON DUPLICATE KEY UPDATE
        param_value = VALUES(param_value),
        update_time = NOW(),
        delsign = 0
    </insert>
</mapper>
