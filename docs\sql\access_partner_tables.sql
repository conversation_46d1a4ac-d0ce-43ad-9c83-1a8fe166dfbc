-- 合作方接口相关数据库表结构
-- 创建时间: 2024-08-02
-- 说明: 为 mega-cloud-access 模块新增的合作方接口功能相关表

-- 1. 合作方加密信息表
CREATE TABLE `access_partner_crypto` (
  `project_app_id` BIGINT NOT NULL COMMENT '项目应用ID，关联project_app表',
  `server_name` VARCHAR(100) NOT NULL COMMENT '服务名称，如gossipharbor-cloud-exchange',
  `crypto_key` VARCHAR(255) NOT NULL COMMENT '加密密钥',
  `md5_json` TEXT COMMENT '存储MD5信息的JSON格式数据',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除 1-已删除',
  PRIMARY KEY (`project_app_id`, `server_name`),
  INDEX `idx_project_app_id` (`project_app_id`),
  INDEX `idx_server_name` (`server_name`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合作方加密信息表';

-- 2. 合作方License激活状态表
CREATE TABLE `access_partner_license_active` (
  `project_app_id` BIGINT NOT NULL COMMENT '项目应用ID，关联project_app表',
  `machine_id` VARCHAR(100) NOT NULL COMMENT '机器唯一标识',
  `server_name` VARCHAR(100) NOT NULL COMMENT '服务名称，如gossipharbor-cloud-exchange',
  `license` VARCHAR(255) NOT NULL COMMENT 'License授权码',
  `state` TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-未激活 1-已激活',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除 1-已删除',
  PRIMARY KEY (`project_app_id`, `machine_id`, `server_name`),
  INDEX `idx_project_app_id` (`project_app_id`),
  INDEX `idx_machine_id` (`machine_id`),
  INDEX `idx_server_name` (`server_name`),
  INDEX `idx_license` (`license`),
  INDEX `idx_state` (`state`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合作方License激活状态表';

-- 3. 合作方License激活记录表
CREATE TABLE `access_partner_license_active_record` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `project_app_id` BIGINT NOT NULL COMMENT '项目应用ID，关联project_app表',
  `inner_ip` VARCHAR(50) NOT NULL COMMENT '内网IP地址',
  `public_ip` VARCHAR(50) NOT NULL COMMENT '公网IP地址',
  `machine_id` VARCHAR(100) NOT NULL COMMENT '机器唯一标识',
  `license` VARCHAR(255) NOT NULL COMMENT 'License授权码',
  `success` TINYINT NOT NULL COMMENT '激活结果：0-失败 1-成功',
  `fail_reason` TEXT COMMENT '失败原因',
  `server_name` VARCHAR(100) NOT NULL COMMENT '服务名称，如gossipharbor-cloud-exchange',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_project_app_id` (`project_app_id`),
  INDEX `idx_machine_id` (`machine_id`),
  INDEX `idx_server_name` (`server_name`),
  INDEX `idx_license` (`license`),
  INDEX `idx_success` (`success`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合作方License激活记录表';

-- 添加外键约束（可选，根据实际需要决定是否添加）
-- ALTER TABLE `access_partner_crypto` ADD CONSTRAINT `fk_crypto_project_app` FOREIGN KEY (`project_app_id`) REFERENCES `project_app` (`id`);
-- ALTER TABLE `access_partner_license_active` ADD CONSTRAINT `fk_license_active_project_app` FOREIGN KEY (`project_app_id`) REFERENCES `project_app` (`id`);
-- ALTER TABLE `access_partner_license_active_record` ADD CONSTRAINT `fk_license_record_project_app` FOREIGN KEY (`project_app_id`) REFERENCES `project_app` (`id`);

-- 示例数据插入（用于测试）
-- INSERT INTO `access_partner_crypto` (`project_app_id`, `server_name`, `crypto_key`, `md5_json`, `create_time`, `update_time`, `delsign`) 
-- VALUES (1, 'gossipharbor-cloud-exchange', 'test-crypto-key-123', '{"config.xml":"d41d8cd98f00b204e9800998ecf8427e","app.properties":"5d41402abc4b2a76b9719d911017c592"}', NOW(), NOW(), 0);
