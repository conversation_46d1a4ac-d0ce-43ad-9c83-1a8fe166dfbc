package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@Accessors(chain = true)
@ApiModel(value = "服务创建请求参数")
public class AdminServicesCreateReqVO {
    @NotNull(message = "微服务组id不能为空")
    @ApiModelProperty(value = "微服务组id，不能为空", required = true)
    private Long serviceGroupId;

    @NotNull(message = "目标服务器id不能为空")
    @ApiModelProperty(value = "目标服务器id（ecs_server_id），不能为空", required = true)
    private Long targetEcsServerId;

    @ApiModelProperty(value = "Jenkins 构建参数")
    private Map<String, String> jenkinsParams;

    @NotNull(message = "操作人admin用户id不能为空")
    @ApiModelProperty(value = "操作人admin用户id", required = true)
    private Long adminUserId;

    @NotNull(message = "微服务名称不能为空")
    @ApiModelProperty(value = "微服务名称，不能为空", required = true)
    private String servicesName;

    @ApiModelProperty(value = "程序路径")
    private String path;

    @ApiModelProperty(value = "日志路径")
    private String logPath;

    @ApiModelProperty(value = "服务描述")
    private String description;

    @ApiModelProperty(value = "日志超时时间（秒）")
    private Integer logTimeoutSecond;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "备注信息")
    private String remark;
}
