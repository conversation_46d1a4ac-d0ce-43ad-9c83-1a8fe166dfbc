package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreateJenkinsUserDTO extends JenkinsGroovyDTO{
    private final static String groovyScriptFormat = "import jenkins.model.*\n" +
            "import hudson.security.*\n" + "\n" +
            "def instance = Jenkins.getInstance()\n" +
            "def hudsonRealm = instance.getSecurityRealm()\n" + "\n" +
            "// 用户参数\n" +
            "def username = \"%s\"\n" +
            "def password = \"Mangosteen0!\"\n" +
            "def fullname = \"%s\"\n" +
            "def email = \"%s\"\n" + "\n" +
            "// 创建用户\n" +
            "if (hudsonRealm instanceof HudsonPrivateSecurityRealm) {\n" +
            "    def user = hudsonRealm.createAccount(username, password)\n" +
            "    user.setFullName(fullname)\n" +
            "    user.addProperty(new hudson.tasks.Mailer.UserProperty(email))\n" +
            "    user.save()\n" +
            "    println \"用户 ${username} 创建成功\"\n" +
            "} else {\n" +
            "    println \"当前安全域不支持直接创建用户\"\n" +
            "}";

    private String email;
    private String userName;

    public CreateJenkinsUserDTO(String email) {
        this.email = email;
        if (email.contains("@")) {
            String[] parts = email.split("@")[0].split("\\.");
            if (parts.length == 2) {
                this.userName = "bot-" + parts[1] + parts[0];
            } else {
                this.userName = "bot-" + email.split("@")[0]; // 不规则直接取名部分
            }
        } else {
            this.userName = "bot-" + email;
        }
    }

    public CreateJenkinsUserDTO(String email, String userName) {
        this.email = email;
        this.userName = userName;
    }

    @Override
    public String getGroovyScriptStr() {
        return String.format(groovyScriptFormat, userName, userName, email);
    }
}
