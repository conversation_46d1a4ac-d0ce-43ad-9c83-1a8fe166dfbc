# mega-cloud-admin 服务组需求

在mega-cloud-admin模组中的AdminServiceManageController.java实现接口定义，在AdminServiceGroupManageService实现Service逻辑。新建AdminServiceGroupDao和对应的mybatis xml。
在vo中建立reqVO和respVO

## 接口列表

### 1. 创建组
/admin/api/{projectId}/microservice/service/group/create

#### 入参：
@PathVariable("projectId") Long projectId 以及对象AdminServicesGroupCreateReqVO。
AdminCreateServicesGroupReqVO属性如下：
@ApiModelProperty("模板参数值")
private Map<String, String> jenkinsParams;
@ApiModelProperty("模板id")
private Long jenkinsTemplateId;
@NotNull
@ApiModelProperty("jenkins实例id")
private Long jenkinsServiceId;
@NotNull
@ApiModelProperty("服务组名")
private String servicesGroupName;
@NotNull
@ApiModelProperty("启动方式")
private Integer serviceUpdateId;
@NotNull
@ApiModelProperty("环境")
private String serviceEnv;
@ApiModelProperty("保活数量")
private Integer serviceAliveNum = 0;
@ApiModelProperty("保活检测方式")
private Integer checkAliveType = 1;
private String remark;

private List<BigInt> tags;


#### 出参： 
无

#### service逻辑：
调用mega-cloud-client模组提供的ServicesGroupClient.java中的createServicesGroup方法，

获得servicesGroupId后，再更新再services_group表中有，在CreateServicesGroupReqVO中没有的字段。

### 2. 编辑组信息
/admin/api/{projectId}/microservice/service/group/edit

#### 入参：
与创建组入参一致

#### 出参：
无

#### service逻辑：
更新数据库`services_group`表
if （无services） ：{
     所有字段都可改
} else {
     备注、n保活数量、管理员、is_self、services_log_format_id、sort、组标签
}

### 3. 更改组状态
/admin/api/{projectId}/microservice/service/group/status/edit

#### 入参：
@PathVariable("projectId") Long projectId 以及 AdminServicesGroupStatusEditReqVO。

AdminServicesGroupStatusEditReqVO只有一个参数：

* @ApiModelProperty("状态 0：下线 1：上线")
private Integer status;


#### 出参：
无

#### service逻辑：
下线：
有正在运行的services不让下线，需要先全部停止

上线：
* 是否有services


### 4. 重启组
/admin/api/{projectId}/microservice/service/group/restart

#### 入参：
@PathVariable("projectId") Long projectId 以及AdminServicesGroupActionReqVO。

AdminServicesGroupActionReqVO参数：

* servicesGroupId
* action integer // NOTHING(0, "nothing", ""), RESTART(1, "restart", "重启"), STOP(2, "stop", "停止")

#### 出参：
无

#### service逻辑：
调用mega-cloud-client模组提供的ServicesGroupClient.java中的buildServicesGroup方法


### 5. 停止组
/admin/api/{projectId}/microservice/service/group/stop

入参出参service逻辑同4. 重启组

### 6. 删除组
/admin/api/{projectId}/microservice/service/group/delete

入参出参同4. 重启组

service逻辑：
把`services_group`表的`delsign`更为1