package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 基于组的services查询请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "基于组的services查询请求参数", description = "基于组的services查询请求参数")
public class AdminServicesListByGroupReqVO {

    @NotNull(message = "服务组ID不能为空")
    @ApiModelProperty(value = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;
}