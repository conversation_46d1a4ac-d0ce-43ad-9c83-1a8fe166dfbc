
-- player表， id为big int 雪花算法生成

CREATE TABLE `character` (
    `id` BIGINT NOT NULL COMMENT '玩家ID,雪花算法生成',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `level` INT DEFAULT 1 COMMENT '玩家等级',
    `exp` BIGINT DEFAULT 0 COMMENT '经验值',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家信息表';
