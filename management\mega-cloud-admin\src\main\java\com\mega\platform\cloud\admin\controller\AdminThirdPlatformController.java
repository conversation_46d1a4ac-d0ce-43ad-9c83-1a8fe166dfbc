package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.common.mapper.ThirdPlatformMapper;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.entity.ThirdPlatform;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "三方平台管理")
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/api/public/third/platform")
public class AdminThirdPlatformController {
    private final ThirdPlatformMapper thirdPlatformMapper;

    @ApiOperation("Payment配置管理 获取支持的平台列表（如微信、苹果等）")
    @PostMapping("/list")
    public Result<List<ThirdPlatform>> listSupportedPlatforms() {
        List<ThirdPlatform> configs = thirdPlatformMapper.select(new ThirdPlatform().setDelsign((byte) 0));
        return Results.success(configs);
    }
}
