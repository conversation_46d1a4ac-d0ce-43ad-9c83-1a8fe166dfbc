<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.platform.cloud.monitor.dao.MetricsDao">
    <select id="getMonitorMetricsMapByIds" resultType="com.mega.platform.cloud.data.entity.MonitorMetrics">
        SELECT *
        FROM monitor_metrics
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getMetricsRuleDTOScanList" resultType="com.mega.platform.cloud.data.dto.monitor.MetricsRuleDTO">
        SELECT t1.*,
               t2.`key` AS metricsKey,
               t2.type  AS metricsType
        FROM monitor_rule AS t1
                 LEFT JOIN monitor_metrics AS t2 ON t1.monitor_metrics_id = t2.id
        WHERE t1.is_enabled = 1
          AND t1.delsign = 0
          AND t1.status = 1
    </select>
    <select id="getMetricsAlarmDTOScanList" resultType="com.mega.platform.cloud.data.dto.monitor.MetricsAlarmDTO">
        SELECT t1.*,
               t2.alarm_interval_second AS alarmIntervalSecond
        FROM monitor_alarm AS t1
                 LEFT JOIN monitor_rule AS t2 ON t1.monitor_rule_id = t2.id
        WHERE t1.status IN (1, 2)
          AND t1.delsign = 0
          AND t2.delsign = 0
          AND t2.is_enabled = 1
    </select>
    <insert id="insertMonitorAlarm" useGeneratedKeys="true" keyColumn="id" keyProperty="monitorAlarm.id">
        INSERT INTO monitor_alarm (monitor_rule_id, monitor_metric_id, actual_value, status, trigger_time)
        VALUES (#{monitorAlarm.monitorRuleId}, #{monitorAlarm.monitorMetricId}, #{monitorAlarm.actualValue}, #{monitorAlarm.status}, #{monitorAlarm.triggerTime})
    </insert>
    <select id="getMetricsNotifyDTOByRuleId" resultType="com.mega.platform.cloud.data.dto.monitor.MetricsNotifyDTO">
        SELECT t1.*,
               t3.name AS metricsName,
               t2.name AS ruleName,
               t5.name AS servicesName,
               t5.id AS servicesId,
               t2.source_type AS sourceType,
               t4.id AS ecsId,
               t4.name AS ecsName,
               t2.level,
               t6.name AS servicesGroupName,
               t7.name AS projectName
        FROM monitor_notify AS t1
                 LEFT JOIN monitor_rule AS t2 ON t1.monitor_rule_id = t2.id
                 LEFT JOIN monitor_metrics AS t3 oN t2.monitor_metrics_id = t3.id
                 LEFT JOIN ecs_server AS t4 ON t2.source_type = 1 AND t2.source_id = t4.id
                 LEFT JOIN services AS t5 ON t2.source_type = 2 AND t2.source_id = t5.id
                 LEFT JOIN services_group AS t6 ON t5.services_group_id = t6.id
                 LEFT JOIN project AS t7 ON t2.project_id = t7.id
        WHERE t1.delsign = 0
          AND t2.delsign = 0
          AND t2.is_enabled = 1
          AND t3.delsign = 0
    </select>
    <select id="getMetricsRuleDTOByRuleId" resultType="com.mega.platform.cloud.data.dto.monitor.MetricsRuleDTO">
        SELECT t1.*,
               t2.`key` AS metricsKey,
               t2.type  AS metricsType,
               t1.source_id,
               t1.source_type,
               CASE
                   WHEN t1.source_type = 1 THEN t4.name
                   WHEN t1.source_type = 2 THEN t3.name
                   END AS source_name
        FROM monitor_rule AS t1
                 LEFT JOIN monitor_metrics AS t2 ON t1.monitor_metrics_id = t2.id
                 LEFT JOIN services AS t3 ON t1.source_type = 2 AND t1.source_id = t3.id
                 LEFT JOIN ecs_server AS t4 ON t1.source_type = 1 AND t1.source_id = t4.id
        WHERE t1.is_enabled = 1
          AND t1.id = #{ruleId}
          AND t1.delsign = 0
    </select>
</mapper>