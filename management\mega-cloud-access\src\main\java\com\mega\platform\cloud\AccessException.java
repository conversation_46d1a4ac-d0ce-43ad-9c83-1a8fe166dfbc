package com.mega.platform.cloud;


import com.mega.platform.cloud.core.exception.BaseException;

import java.util.Objects;

public class AccessException extends BaseException {

    public AccessException(Integer code) {
        this(Objects.requireNonNull(AccessErrorCode.getExchangeCode(code)));
    }

    public AccessException(AccessErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public AccessException(Integer code, String message) {
        super(code, message);
    }
}
