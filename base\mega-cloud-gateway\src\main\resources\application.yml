server:
  port: 8082
  address: 0.0.0.0  # 允许所有 IP 访问
spring:
  profiles:
    active: dev
  application:
    name: gateway-${spring.profiles.active}
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          filters:
            - StripPrefix=1
            - name: Retry
              args:
                methods:
                  - "'GET'"
                  - "'POST'"
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: false