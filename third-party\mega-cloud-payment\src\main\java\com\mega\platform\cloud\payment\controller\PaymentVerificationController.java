package com.mega.platform.cloud.payment.controller;

import com.mega.platform.cloud.client.payment.PaymentVerificationClient;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.payment.*;
import com.mega.platform.cloud.payment.service.PaymentAlipayVerificationService;
import com.mega.platform.cloud.payment.service.PaymentAppleVerificationService;
import com.mega.platform.cloud.payment.service.PaymentWeChatVerificationService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@Api(tags = "支付相关接口")
@Slf4j
@RestController
@RequiredArgsConstructor
public class PaymentVerificationController implements PaymentVerificationClient {
    private final PaymentAppleVerificationService paymentAppleVerificationService;
    private final PaymentAlipayVerificationService paymentAlipayVerificationService;
    private final PaymentWeChatVerificationService paymentWeChatVerificationService;
    @Override
    public Result<PaymentAppleVerifyTransactionRespVO> verifyAppleTransaction(PaymentAppleVerifyTransactionReqVO vo) throws IOException {
        return Results.success(paymentAppleVerificationService.verifyAppleTransaction(vo));
    }

    @Override
    public Result<PaymentAppleCallbackRespVO> appleCallback(PaymentAppleCallbackReqVO vo){
        return Results.success(paymentAppleVerificationService.appleCallback(vo));
    }

    @Override
    public Result<PaymentAlipayCreateRespVO> createAlipayTransaction(PaymentAlipayCreateReqVO vo) {
        return Results.success(paymentAlipayVerificationService.createAlipayTransaction(vo));
    }

    @Override
    public Result<PaymentAlipayCallbackRespVO> alipayCallback(PaymentAlipayCallbackReqVO vo) {
        return Results.success(paymentAlipayVerificationService.alipayCallback(vo));
    }

    @Override
    public Result<PaymentWeChatCreateRespVO> createWeChatTransaction(PaymentWeChatCreateReqVO vo) {
        return Results.success(paymentWeChatVerificationService.createWeChatTransaction(vo));
    }

    @Override
    public Result<PaymentWeChatCallbackRespVO> wechatCallback(PaymentWeChatCallbackReqVO vo) {
        return Results.success(paymentWeChatVerificationService.wechatCallback(vo));
    }
}
