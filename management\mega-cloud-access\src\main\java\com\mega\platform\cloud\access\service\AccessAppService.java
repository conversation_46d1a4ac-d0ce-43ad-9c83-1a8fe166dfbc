package com.mega.platform.cloud.access.service;

import com.mega.platform.cloud.AccessErrorCode;
import com.mega.platform.cloud.AccessException;
import com.mega.platform.cloud.common.mapper.ProjectAppMapper;
import com.mega.platform.cloud.common.utils.JwtTokenUtil;
import com.mega.platform.cloud.data.entity.ProjectApp;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenReqVO;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenRespVO;
import com.mega.platform.cloud.data.vo.BaseReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AccessAppService {
    private final ProjectAppMapper projectAppMapper;
    private final JwtTokenUtil jwtTokenUtil;

    @Autowired
    public AccessAppService(ProjectAppMapper projectAppMapper, JwtTokenUtil jwtTokenUtil) {
        this.projectAppMapper = projectAppMapper;
        this.jwtTokenUtil = jwtTokenUtil;
    }

    public AccessAppTokenRespVO accessAppToken(AccessAppTokenReqVO vo) {
        AccessAppTokenRespVO respVO = new AccessAppTokenRespVO();
        ProjectApp projectApp = projectAppMapper.selectOne(new ProjectApp().setAppKey(vo.getAppKey()).setAppSecret(vo.getAppSecret()).setDelsign((byte) 0));
        if (projectApp == null) throw new AccessException(AccessErrorCode.ERR_4000);
        BaseReqVO baseReqVO = new BaseReqVO();
        baseReqVO.setAppId(projectApp.getId());
        respVO.setToken(jwtTokenUtil.generateToken(baseReqVO));
        return respVO;
    }
}
