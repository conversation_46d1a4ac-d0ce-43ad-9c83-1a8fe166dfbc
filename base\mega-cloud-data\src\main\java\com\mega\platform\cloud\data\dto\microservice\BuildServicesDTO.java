package com.mega.platform.cloud.data.dto.microservice;

import com.mega.platform.cloud.data.entity.JenkinsServices;
import com.mega.platform.cloud.data.entity.JenkinsUser;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BuildServicesDTO {

    private Long servicesId;
    private Integer action;
    private Long adminUserId;
    private Boolean checkRunningStatus = true;
    private Long servicesGroupId;
}
