package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_order_device_info")
public class PaymentOrderDeviceInfo {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Integer id;

    /**
     * 关联的支付订单ID
     */
    @Column(name = "payment_order_id")
    private Long paymentOrderId;

    /**
     * 设备uuid
     */
    @Column(name = "device_uuid")
    private String deviceUuid;

    /**
     * 设备名称
     */
    @Column(name = "device_name")
    private String deviceName;

    /**
     * 操作系统
     */
    @Column(name = "device_os")
    private String deviceOs;

    /**
     * 操作系统版本
     */
    @Column(name = "device_os_version")
    private String deviceOsVersion;

    /**
     * 设备具体型号
     */
    @Column(name = "device_model")
    private String deviceModel;

    /**
     * 应用版本号
     */
    @Column(name = "bvrs")
    private String bvrs;

    /**
     * IP地址
     */
    @Column(name = "ip_address")
    private String ipAddress;

    /**
     * 网络类型
     */
    @Column(name = "network_type")
    private String networkType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}