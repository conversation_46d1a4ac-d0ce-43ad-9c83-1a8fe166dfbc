package com.mega.platform.cloud.data.vo.payment;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("验证基础请求参数")
@Accessors(chain = true)
public class PaymentBaseReqVO extends BaseReqVO {
    @ApiModelProperty(value = "设备UUID", required = true)
    @NotBlank(message = "设备UUID不能为空")
    private String deviceUuid;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "操作系统ID")
    private Integer deviceOsId;

    @ApiModelProperty(value = "操作系统版本")
    private String deviceOsVersion;

    @ApiModelProperty(value = "设备型号")
    private String deviceModel;

    @ApiModelProperty(value = "应用版本号")
    private String bvrs;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "网络类型")
    private String networkType;
}
