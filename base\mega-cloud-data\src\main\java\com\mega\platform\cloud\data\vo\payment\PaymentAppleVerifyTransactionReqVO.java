package com.mega.platform.cloud.data.vo.payment;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
@Data
@ApiModel("苹果交易验证请求参数")
@Accessors(chain = true)
public class PaymentAppleVerifyTransactionReqVO extends PaymentBaseReqVO {
    @ApiModelProperty(value = "苹果交易id", required = true)
    @NotBlank(message = "苹果交易id")
    private String transactionId;
}
