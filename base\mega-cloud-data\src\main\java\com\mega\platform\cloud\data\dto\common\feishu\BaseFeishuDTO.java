package com.mega.platform.cloud.data.dto.common.feishu;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class BaseFeishuDTO {

    private String title = "未定义title"; // 标题
    private Integer level = 0; // 优先级
    private List<String> tags = new ArrayList<>(); // 关键字
    private Map<String, String> dataMap;
    private List<String> at = new ArrayList<>(); // at
    private List<String> botUrls = new ArrayList<>(); // botUrl
    private String emoJi; // 小表情


    public void addTag(String tag) {
        this.tags.add(tag);
    }

    public void addAt(String userId) {
        this.at.add(userId);
    }

    public void addBotUrl(String boturl) {
        this.botUrls.add(boturl);
    }

    public String getLevelTag(Integer level) {
        if (level == 1) {
            return "<text_tag color='" + getTagColor(level) + "'>优先级：低</text_tag>";
        } else if (level == 2) {
            return "<text_tag color='" + getTagColor(level) + "'>优先级：中</text_tag>";
        } else if (level == 3) {
            return "<text_tag color='" + getTagColor(level) + "'>优先级：高</text_tag>";
        } else return "";
    }

    public String getTagColor(Integer level) {
        if (level == 1) {
            return "blue";
        } else if (level == 2) {
            return "orange";
        } else if (level == 3) {
            return "carmine";
        } else return "green";
    }

    public String getLevelFont(Integer level) {
        if (level == 1) {
            return "x-large";
        } else if (level == 2) {
            return "xx-large";
        } else if (level == 3) {
            return "xxx-large";
        } else return "";
    }
}