package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_job")
public class JenkinsJob {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * jenkins服务ID
     */
    @Column(name = "jenkins_services_id")
    private Long jenkinsServicesId;

    /**
     * Jenkins视图ID，关联jenkins_view(id)
     */
    @Column(name = "jenkins_view_id")
    private Long jenkinsViewId;

    @Column(name = "jenkins_ssh_server_id")
    private Long jenkinsSshServerId;

    /**
     * Jenkins Job名称
     */
    @Column(name = "job_name")
    private String jobName;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}