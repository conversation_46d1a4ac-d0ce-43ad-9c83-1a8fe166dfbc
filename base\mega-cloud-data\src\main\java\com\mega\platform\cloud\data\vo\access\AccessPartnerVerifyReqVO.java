package com.mega.platform.cloud.data.vo.access;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("合作方AES密钥获取请求参数")
public class AccessPartnerVerifyReqVO {
    @ApiModelProperty(value = "appKey", example = "app_1752202145", required = true)
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    @ApiModelProperty(value = "服务名称", example = "gossipharbor-cloud-exchange", required = true)
    @NotBlank(message = "服务名称不能为空")
    private String serverName;

    @ApiModelProperty(value = "文件MD5列表", required = true)
    @NotEmpty(message = "MD5列表不能为空")
    @Valid
    private List<AccessMd5VO> md5List;
}
