package com.mega.platform.cloud.client.scheduler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.client.access.AccessAppClient;
import com.mega.platform.cloud.client.config.AppTokenProperties;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenReqVO;
import com.mega.platform.cloud.data.vo.access.AccessAppTokenRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Component
@RequiredArgsConstructor
@Slf4j
public class AppTokenManagerClient {
    private final AccessAppClient accessAppClient;
    private final AppTokenProperties appTokenProperties;
    private volatile String token; // 当前 token
    private volatile Instant expireTime = Instant.now(); // 当前 token 的过期时间

    public String getToken() throws JsonProcessingException {
        if (token == null || Instant.now().isAfter(expireTime.minusSeconds(60))) {
            refreshToken();
        }
        return token;
    }

    @Scheduled(fixedDelay = 5 * 60 * 1000) // 每5分钟尝试刷新
    public synchronized void refreshToken() throws JsonProcessingException {
        AccessAppTokenReqVO req = new AccessAppTokenReqVO();
        req.setAppKey(appTokenProperties.getKey());
        req.setAppSecret(appTokenProperties.getSecret());

        Result<AccessAppTokenRespVO> result = accessAppClient.accessAppToken(req);
        log.info("result:{}", new ObjectMapper().writeValueAsString(result));
        if (result.getCode() == 1) {
            this.token = result.getData().getToken();
            this.expireTime = Instant.now().plusSeconds(3600);
        }
    }
}
