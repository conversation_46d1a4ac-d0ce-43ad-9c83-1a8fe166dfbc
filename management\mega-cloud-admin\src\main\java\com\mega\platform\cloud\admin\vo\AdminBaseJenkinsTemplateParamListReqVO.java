package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel("jenkins模板参数列表请求数据")
public class AdminBaseJenkinsTemplateParamListReqVO {
    @ApiModelProperty("模板ID，关联jenkins_job_template(id)")
    @NotNull
    private Long jenkinsTemplateId;

    @ApiModelProperty("参数类型 1-group上的 2-service上的")
    @NotNull
    private Integer servicesDataType;
}
