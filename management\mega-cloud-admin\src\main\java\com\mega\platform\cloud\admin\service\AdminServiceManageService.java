package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.vo.AdminServicesCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesEditReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesStatusEditReqVO;
import com.mega.platform.cloud.admin.vo.AdminServicesHandleReqVO;
import com.mega.platform.cloud.client.microservice.ServicesClient;
import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.common.mapper.JenkinsJobTemplateParamValueMapper;
import com.mega.platform.cloud.common.mapper.ServicesGroupMapper;
import com.mega.platform.cloud.common.mapper.ServicesMapper;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTemplateParamDTO;
import com.mega.platform.cloud.data.entity.JenkinsJobTemplateParamValue;
import com.mega.platform.cloud.data.entity.Services;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesRespVO;
import com.mega.platform.cloud.microservice.service.ServicesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mega.platform.cloud.common.constant.MicroserviceConstants.SERVICES_DATA_TYPE_SERVICES;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminServiceManageService {
    private final ServicesService servicesService;
    private final ServicesMapper servicesMapper;
    private final ServicesClient servicesClient;
    private final ServicesGroupMapper servicesGroupMapper;
    private final JenkinsJobTemplateParamValueMapper jenkinsJobTemplateParamValueMapper;

    /**
     * admin创建服务
     * @param reqVO
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void createService(AdminServicesCreateReqVO reqVO) throws Exception {
        CreateServicesReqVO createServicesReqVO =  new CreateServicesReqVO();
        BeanUtils.copyProperties(reqVO, createServicesReqVO);
        CreateServicesRespVO createServicesRespVO = servicesService.createServices(createServicesReqVO);
        Services services = servicesMapper.selectByPrimaryKey(createServicesRespVO.getServicesId());
        // 更新部分字段
        services.setPath(reqVO.getPath());
        services.setLogPath(reqVO.getLogPath());
        services.setDescription(reqVO.getDescription());
        services.setLogTimeoutSecond(reqVO.getLogTimeoutSecond());
        services.setSort(reqVO.getSort());
        services.setRemark(reqVO.getRemark());
        servicesMapper.updateByPrimaryKeySelective(services);
    }

    /**
     * admin编辑服务
     * @param reqVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void editService(AdminServicesEditReqVO reqVO) {
        // 查询服务
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services == null) {
            throw new AdminException(0, "服务不存在");
        }

        // 业务规则校验
        Map<String, String> jenkinsParams = reqVO.getJenkinsParams();
        if (isServicePublished(services) && hasRestrictedFieldChanges(reqVO, jenkinsParams)) {
            throw new AdminException(0, "请下线服务后重试");
        }

        // 更新服务基本信息
        BeanUtils.copyProperties(reqVO, services);
        servicesMapper.updateByPrimaryKeySelective(services);

        // 处理Jenkins参数
        if (jenkinsParams != null) {
            updateJenkinsParams(services, reqVO.getServiceGroupId(), jenkinsParams);
        }
    }

    /**
     * 上线下线服务
     * @param reqVO
     */
    public void editServiceStatus(AdminServicesStatusEditReqVO reqVO) {
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services != null) {
            if (reqVO.getStatus() == 0) {
                // 下线
                if (isServiceOnline(services)) {
                    throw new AdminException(0, "服务为在线状态");
                }
            } else if (reqVO.getStatus() == 1) {
                // 上线
                // TODO 参数检查
            }
            services.setStatus(reqVO.getStatus());
            servicesMapper.updateByPrimaryKeySelective(services);
        } else {
            throw new AdminException(0, "服务不存在");
        }
    }

    /**
     * 停止服务
     * @param reqVO
     * @param adminUserId
     * @throws Exception
     */
    public void stopService(AdminServicesHandleReqVO reqVO, Long adminUserId) throws Exception {
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services != null) {
            BuildServicesReqVO buildServicesReqVO = new BuildServicesReqVO();
            buildServicesReqVO.setAdminUserId(adminUserId).setServicesId(reqVO.getServicesId()).setAction(ServiceGroupBuildActionEnum.STOP.getAction());
            servicesClient.buildServices(buildServicesReqVO);
        } else {
            throw new AdminException(0, "服务不存在");
        }
    }

    /**
     * 删除服务
     * @param reqVO
     */
    public void deleteService(AdminServicesHandleReqVO reqVO) {
        Services services = servicesMapper.selectOne(new Services().setId(reqVO.getServicesId()).setDelsign((byte) 0));
        if (services != null) {
            services.setDelsign((byte) 1);
            servicesMapper.updateByPrimaryKeySelective(services);
        } else {
            throw new AdminException(0, "服务不存在");
        }
    }

    private boolean hasRestrictedFieldChanges(AdminServicesEditReqVO reqVO, Map<String, String> jenkinsParams) {
        return reqVO.getPath() != null
                || reqVO.getServiceGroupId() != null
                || reqVO.getEcsServerId() != null
                || jenkinsParams != null
                || reqVO.getName() != null;
    }

    private void updateJenkinsParams(Services services, Long serviceGroupId, Map<String, String> jenkinsParams) {
        // 校验服务组存在性
        if (serviceGroupId == null) {
            throw new AdminException(0, "服务组ID不能为空");
        }

        ServicesGroup servicesGroup = servicesGroupMapper.selectByPrimaryKey(serviceGroupId);
        if (servicesGroup == null) {
            throw new AdminException(0, "服务组不存在");
        }

        // 验证模板参数
        List<JenkinsTemplateParamDTO> templateServicesParams = servicesService
                .checkTemplateParamKey(servicesGroup.getJenkinsTemplateId(), SERVICES_DATA_TYPE_SERVICES, jenkinsParams);

        // 循环更新参数
        for (JenkinsTemplateParamDTO templateParam : templateServicesParams) {
            JenkinsJobTemplateParamValue paramValue = new JenkinsJobTemplateParamValue();
            paramValue.setJenkinsJobTempleteParamId(templateParam.getJenkinsTemplateParamId());
            paramValue.setServicesDataId(services.getId());
            paramValue.setServicesDataType(SERVICES_DATA_TYPE_SERVICES);
            paramValue.setParamValue(jenkinsParams.get(templateParam.getParamKey()));
            jenkinsJobTemplateParamValueMapper.updateByPrimaryKeySelective(paramValue);
        }
    }

    /**
     * 检测服务是否上线
     * @param services
     * @return
     */
    private boolean isServicePublished(Services services) {
        // 业务逻辑判断服务是否上线
        return services.getStatus() == 1;
    }

    /**
     * 检测服务是否在线
     * @param services
     * @return
     */
    private boolean isServiceOnline(Services services) {
        return !(services.getRunningStatus() == 0 && services.getRealRunningStatus() == 0);
    }



}
