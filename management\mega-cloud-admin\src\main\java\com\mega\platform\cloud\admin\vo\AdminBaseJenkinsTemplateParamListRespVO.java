package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.JenkinsJobTemplateParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("jenkins模板参数列表响应数据")
public class AdminBaseJenkinsTemplateParamListRespVO {
    @ApiModelProperty(value = "jenkins模板参数列表")
    private List<JenkinsJobTemplateParam> jenkinsJobTemplateParams;
}