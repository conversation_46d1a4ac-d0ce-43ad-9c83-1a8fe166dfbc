package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum AccessErrorCode {

    ERR_0(0),
    ERR_4000(4000),
    ERR_4001(4001),
    ERR_4002(4002),
    ;

    private final Integer code;

    AccessErrorCode(Integer code) {
        this.code = code;
    }

    public static AccessErrorCode getExchangeCode(Integer code) {
        for (AccessErrorCode exchangeCode : AccessErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
