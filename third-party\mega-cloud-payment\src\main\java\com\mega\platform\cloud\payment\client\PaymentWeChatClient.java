package com.mega.platform.cloud.payment.client;

import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.PaymentWechatConfig;
import com.mega.platform.cloud.payment.cache.PaymentAppConfigCache;
import com.mega.platform.cloud.payment.entity.PaymentWeChatClientResult;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RSAPublicKeyNotificationConfig;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.partnerpayments.app.model.Transaction;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.app.model.Amount;
import com.wechat.pay.java.service.payments.app.model.PrepayRequest;
import com.wechat.pay.java.service.payments.app.model.PrepayResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentWeChatClient {
    private final PaymentAppConfigCache paymentAppConfigCache;

    /**
     * 创建微信交易（APP 支付）
     */
    public PaymentWeChatClientResult createWeChatTransaction(String outTradeNo, String totalAmountFen, String description, Long appId) {
        PaymentWeChatClientResult result = new PaymentWeChatClientResult();
        try {
            PaymentWechatConfig config = paymentAppConfigCache.getTypedConfig(appId, ThirdPlatformEnum.WECHAT.getCode(), PaymentWechatConfig.class);

            // 构造微信 SDK 配置
            Config wechatConfig = buildWechatPayConfig(config);
            AppService service = new AppService.Builder().config(wechatConfig).build();

            // 构造下单请求
            PrepayRequest request = new PrepayRequest();
            request.setAppid(config.getAppId());
            request.setMchid(config.getMerchantId());
            request.setOutTradeNo(outTradeNo);
            request.setDescription(description);
            request.setNotifyUrl(config.getNoticeUrl());

            Amount amount = new Amount();
            amount.setTotal(Integer.parseInt(totalAmountFen));
            amount.setCurrency("CNY");
            request.setAmount(amount);

            // 下单并返回 prepayId
            PrepayResponse response = service.prepay(request);
            result.setSuccess(true);
            result.setPrepayId(response.getPrepayId());
        } catch (Exception e) {
            log.error("创建微信支付交易异常", e);
            result.setSuccess(false);
            result.setErrorMessage("创建交易异常: " + e.getMessage());
        }
        return result;
    }

    /**
     * 微信回调处理
     */
    public PaymentWeChatClientResult wechatNotificationCallback(String serial, String signature, String timestamp, String nonce, String body, Long appId) {
        PaymentWeChatClientResult result = new PaymentWeChatClientResult();
        try {
            PaymentWechatConfig config = paymentAppConfigCache.getTypedConfig(appId, ThirdPlatformEnum.WECHAT.getCode(), PaymentWechatConfig.class);

            NotificationConfig wechatConfig = (NotificationConfig) buildWechatPayConfig(config);
            NotificationParser parser = new NotificationParser(wechatConfig);

            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(serial)
                    .nonce(nonce)
                    .signature(signature)
                    .timestamp(timestamp)
                    .body(body)
                    .build();

            Transaction transaction = parser.parse(requestParam, Transaction.class);

            log.info("收到微信支付通知，outTradeNo={}, tradeState={}", transaction.getOutTradeNo(), transaction.getTradeState());

            result.setSuccess(true);
            result.setTransaction(transaction);
        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            result.setSuccess(false);
            result.setErrorMessage("回调处理异常: " + e.getMessage());
        }
        return result;
    }

    private Config buildWechatPayConfig(PaymentWechatConfig config) throws IOException {
        // 读取商户私钥（只读一次）
        Resource resource = new ClassPathResource(config.getPrivateKeyPath());
        String privateKey = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(config.getMerchantId())
                .privateKey(privateKey)
                .merchantSerialNumber(config.getMerchantSerialNumber())
                .apiV3Key(config.getApiV3Key())
                .build();
    }

}
