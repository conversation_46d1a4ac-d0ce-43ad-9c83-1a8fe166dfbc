package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum PushErrorCode {

    ERR_0(0),
    ;

    private final Integer code;

    PushErrorCode(Integer code) {
        this.code = code;
    }

    public static PushErrorCode getExchangeCode(Integer code) {
        for (PushErrorCode exchangeCode : PushErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
