package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdminAppAuthSmsReqVO {
    @ApiModelProperty("应用ID")
    private Long projectAppId;

    @ApiModelProperty("第三方通用平台配置")
    private Long thirdPlatformId;

    @ApiModelProperty("验证码用途类型，例如 login、register、reset_pwd、bind_phone 等")
    private String type;

    @ApiModelProperty("模版code 查列表时不传")
    private String smsTemplateCode;
}
