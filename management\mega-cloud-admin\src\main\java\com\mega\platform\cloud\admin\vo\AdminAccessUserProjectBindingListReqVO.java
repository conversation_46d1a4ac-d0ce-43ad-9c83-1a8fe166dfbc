package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-管理员项目绑定列表请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员项目绑定列表请求")
public class AdminAccessUserProjectBindingListReqVO {

    @ApiModelProperty("管理员ID")
    @NotNull(message = "管理员ID不能为空")
    private Long adminUserId;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}