package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 全部services列表查询响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "全部services列表查询响应参数", description = "全部services列表查询响应参数")
public class AdminServicesListRespVO {

    // services基本信息
    @ApiModelProperty("服务ID")
    private Long servicesId;

    @ApiModelProperty("服务名称")
    private String servicesName;

    @ApiModelProperty("服务备注")
    private String servicesRemark;

    @ApiModelProperty("服务状态")
    private Integer servicesStatus;

    @ApiModelProperty("服务运行状态")
    private Integer servicesRunningStatus;

    @ApiModelProperty("服务真实运行状态")
    private Integer servicesRealRunningStatus;

    @ApiModelProperty("服务参数列表")
    private Map<String, String> servicesParams;

    // 组的基本信息
    @ApiModelProperty("服务组ID")
    private Long servicesGroupId;

    @ApiModelProperty("服务组名称")
    private String servicesGroupName;

    @ApiModelProperty("服务组状态")
    private Integer servicesGroupStatus;

    @ApiModelProperty("服务组运行状态")
    private Integer servicesGroupRunningStatus;

    @ApiModelProperty("管理员用户ID")
    private Long adminUserId;

    @ApiModelProperty("服务组参数列表")
    private Map<String, String> servicesGroupParams;

    // 组标签
    @ApiModelProperty("组标签列表")
    private List<Long> tags;

    // 最后一次jenkins_task_group信息
    @ApiModelProperty("最后一次任务组ID")
    private Long lastTaskGroupId;

    @ApiModelProperty("最后一次任务组操作类型")
    private Integer lastTaskGroupAction;

    @ApiModelProperty("最后一次任务组是否成功")
    private Integer lastTaskGroupIsSuccess;

    @ApiModelProperty("最后一次任务组完成时间")
    private Date lastTaskGroupCompleteTime;

    // 最后一次jenkins_task信息
    @ApiModelProperty("最后一次任务ID")
    private Long lastTaskId;

    @ApiModelProperty("最后一次任务操作类型")
    private Integer lastTaskAction;

    @ApiModelProperty("最后一次任务是否成功")
    private Integer lastTaskIsSuccess;

    @ApiModelProperty("最后一次任务完成时间")
    private Date lastTaskCompleteTime;

    @ApiModelProperty("最后一次任务Jenkins链接")
    private String lastTaskJenkinsJobUrl;
}