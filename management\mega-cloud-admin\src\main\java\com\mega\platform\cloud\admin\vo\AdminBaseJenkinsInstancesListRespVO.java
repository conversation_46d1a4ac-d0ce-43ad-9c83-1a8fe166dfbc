package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.JenkinsServices;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("日志格式列表响应数据")
public class AdminBaseJenkinsInstancesListRespVO {
    @ApiModelProperty(value = "日志格式列表")
    private List<JenkinsServices> jenkinsServices;
}
