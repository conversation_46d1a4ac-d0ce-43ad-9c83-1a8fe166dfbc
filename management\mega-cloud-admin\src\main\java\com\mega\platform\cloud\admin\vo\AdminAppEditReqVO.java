package com.mega.platform.cloud.admin.vo;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("应用编辑请求参数")
public class AdminAppEditReqVO {
    
    @ApiModelProperty("应用ID")
    @NotNull(message = "应用ID不能为空")
    private Long id;
    
    @ApiModelProperty("应用名称")
    private String name;
    
    @ApiModelProperty("状态：0不可用，1正常，2挂起，3审核中")
    private Integer status;
    
    @ApiModelProperty("描述")
    private String remark;
}