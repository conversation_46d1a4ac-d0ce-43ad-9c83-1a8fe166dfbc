package com.mega.platform.cloud.payment.service;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.verification.VerificationException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.common.enums.OrderStatusEnum;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.PaymentCallbackLogMapper;
import com.mega.platform.cloud.common.mapper.PaymentOrderDeviceInfoMapper;
import com.mega.platform.cloud.common.mapper.PaymentOrderMapper;
import com.mega.platform.cloud.common.mapper.PaymentProductConfigMapper;
import com.mega.platform.cloud.common.utils.EnvUtil;
import com.mega.platform.cloud.data.entity.PaymentCallbackLog;
import com.mega.platform.cloud.data.entity.PaymentOrder;
import com.mega.platform.cloud.data.entity.PaymentOrderDeviceInfo;
import com.mega.platform.cloud.data.entity.PaymentProductConfig;
import com.mega.platform.cloud.data.vo.payment.PaymentAppleCallbackReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAppleCallbackRespVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAppleVerifyTransactionReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAppleVerifyTransactionRespVO;
import com.mega.platform.cloud.payment.client.PaymentAppleClient;
import com.mega.platform.cloud.payment.entity.PaymentAppleClientResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentAppleVerificationService {
    private final PaymentAppleClient paymentAppleClient;
    private final PaymentOrderMapper paymentOrderMapper;
    private final PaymentOrderDeviceInfoMapper paymentOrderDeviceInfoMapper;
    private final PaymentProductConfigMapper paymentProductConfigMapper;
    private final PaymentCallbackLogMapper paymentCallbackLogMapper;

    @Transactional(rollbackFor = Exception.class)
    public PaymentAppleVerifyTransactionRespVO verifyAppleTransaction(PaymentAppleVerifyTransactionReqVO vo) throws IOException {
        PaymentAppleClientResult paymentAppleClientResult = paymentAppleClient.verifyAppleTransaction(vo.getTransactionId(), vo.getAppId());
        PaymentAppleVerifyTransactionRespVO respVO = new PaymentAppleVerifyTransactionRespVO();
        PaymentOrderDeviceInfo paymentOrderDeviceInfo = new PaymentOrderDeviceInfo();
        PaymentOrder paymentOrder = new PaymentOrder();
        if (paymentAppleClientResult.isSuccess()) {
            JWSTransactionDecodedPayload payload = paymentAppleClientResult.getPayload();
            String productId = payload.getProductId();
            respVO.setProductId(productId).setTransactionId(payload.getTransactionId()).setSuccess(true);

            PaymentProductConfig paymentProductConfig = paymentProductConfigMapper.selectOne(new PaymentProductConfig()
                    .setExternalProductId(productId).setThirdPlatformId(ThirdPlatformEnum.APPLE.getCode()));

            paymentOrder.setPaymentProductConfigId(paymentProductConfig.getId()).setOrderNo(payload.getTransactionId())
                    .setTotalAmount(BigDecimal.valueOf(payload.getPrice())).setCurrency(payload.getCurrency())
                    .setStatus((byte) OrderStatusEnum.SUCCESS.getCode()).setSandBox((short) (EnvUtil.isDev() ? 1 : 0))
                    .setPayTime(new Date(payload.getPurchaseDate())).setExtra(new ObjectMapper().writeValueAsString(payload))
                    .setDelsign((byte) 0);
        } else {
            paymentOrder.setOrderNo(vo.getTransactionId()).setDelsign((byte) 0).setStatus((byte) OrderStatusEnum.FAIL.getCode());
            respVO.setTransactionId(vo.getTransactionId()).setSuccess(false);
        }
        paymentOrderMapper.insertSelective(paymentOrder);
        paymentOrderDeviceInfo.setPaymentOrderId(paymentOrder.getId());
        BeanUtils.copyProperties(vo, paymentOrderDeviceInfo);
        paymentOrderDeviceInfoMapper.insertSelective(paymentOrderDeviceInfo);
        return respVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public PaymentAppleCallbackRespVO appleCallback(PaymentAppleCallbackReqVO vo) {
        PaymentAppleCallbackRespVO respVO = new PaymentAppleCallbackRespVO();
        PaymentCallbackLog paymentCallbackLog = new PaymentCallbackLog();
        try {
            PaymentAppleClientResult paymentAppleClientResult = paymentAppleClient.appleNotificationCallback(vo.getSignedPayload(), vo.getAppId());
            paymentCallbackLog.setProjectAppId(vo.getAppId()).setPlatformCode(ThirdPlatformEnum.APPLE.getPlatformCode())
                    .setRawContent(vo.getSignedPayload())
                    .setProcessTime(new Date()).setDelsign((byte) 0);
            JWSTransactionDecodedPayload payload = paymentAppleClientResult.getPayload();
            PaymentOrder paymentOrder = paymentOrderMapper.selectOne(new PaymentOrder().setOrderNo(payload.getTransactionId()));
            paymentCallbackLog.setPaymentOrderId(paymentOrder.getId());
            if (paymentAppleClientResult.isSuccess()) {
                paymentCallbackLog.setOrderNo(payload.getTransactionId()).setParsedData(new ObjectMapper().writeValueAsString(payload))
                        .setStatus("SUCCESS").setNotifyTime(new Date(payload.getPurchaseDate()));
                respVO.setTransactionId(payload.getTransactionId()).setProductId(payload.getProductId())
                        .setType(paymentAppleClientResult.getNotificationType()).setSuccess(true);
            } else {
                paymentCallbackLog.setStatus("FAILED").setErrorMessage(paymentAppleClientResult.getErrorMessage());
                respVO.setSuccess(false);
            }
        } catch (Exception e) {
            paymentCallbackLog.setStatus("FAILED").setErrorMessage(e.getMessage());
            respVO.setSuccess(false);
        }
        paymentCallbackLogMapper.insertSelective(paymentCallbackLog);
        return respVO;
    }
}