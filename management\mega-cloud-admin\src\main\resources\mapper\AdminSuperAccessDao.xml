<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminSuperAccessDao">

    <!-- 查询传入的userId是否是超级管理员(role_id = 1) -->
    <select id="isSuperAdmin" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM admin_user_role_binding aurb
        WHERE aurb.admin_user_id = #{userId}
          AND aurb.admin_role_id = 1
          AND aurb.delsign = 0
    </select>

    <!-- 查询所有项目列表 -->
    <select id="selectAllProjects" resultType="com.mega.platform.cloud.data.entity.Project">
        SELECT *
        FROM project
        WHERE delsign = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有路由列表 -->
    <select id="selectAllRouters" resultType="com.mega.platform.cloud.data.entity.AdminRouter">
        SELECT id, backend_path, frontend_path, frontend_name, description, parent_admin_router_id, create_time, update_time, delsign
        FROM admin_router
        WHERE delsign = 0
        ORDER BY create_time DESC
    </select>

</mapper>