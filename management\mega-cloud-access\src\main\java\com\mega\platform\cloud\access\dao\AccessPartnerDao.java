package com.mega.platform.cloud.access.dao;

import com.mega.platform.cloud.data.entity.AccessPartnerCrypto;
import com.mega.platform.cloud.data.entity.AccessPartnerLicenseActive;
import com.mega.platform.cloud.data.entity.AccessPartnerLicenseActiveRecord;
import com.mega.platform.cloud.data.entity.ProjectApp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 合作方接口数据访问层
 */
@Mapper
public interface AccessPartnerDao {

    /**
     * 根据tokenKey查询项目应用信息
     *
     * @param appKey 令牌密钥
     * @return 项目应用信息
     */
    ProjectApp selectProjectAppByappKey(@Param("appKey") String appKey);

    /**
     * 根据项目应用ID和服务名称查询加密信息
     *
     * @param projectAppId 项目应用ID
     * @param serverName   服务名称
     * @return 加密信息
     */
    AccessPartnerCrypto selectCryptoByProjectAppIdAndServerName(@Param("projectAppId") Long projectAppId,
                                                                @Param("serverName") String serverName);

    /**
     * 根据项目应用ID、机器ID和服务名称查询License激活状态
     *
     * @param projectAppId 项目应用ID
     * @param machineId    机器唯一标识
     * @param serverName   服务名称
     * @return License激活状态
     */
    AccessPartnerLicenseActive selectLicenseActiveByProjectAppIdAndMachineIdAndServerName(
            @Param("projectAppId") Long projectAppId,
            @Param("machineId") String machineId,
            @Param("serverName") String serverName);

    /**
     * 插入License激活状态记录
     *
     * @param licenseActive License激活状态
     * @return 影响行数
     */
    int insertLicenseActive(AccessPartnerLicenseActive licenseActive);

    /**
     * 更新License激活状态
     *
     * @param licenseActive License激活状态
     * @return 影响行数
     */
    int updateLicenseActive(AccessPartnerLicenseActive licenseActive);

    /**
     * 插入License激活记录
     *
     * @param licenseActiveRecord License激活记录
     * @return 影响行数
     */
    int insertLicenseActiveRecord(AccessPartnerLicenseActiveRecord licenseActiveRecord);
}
