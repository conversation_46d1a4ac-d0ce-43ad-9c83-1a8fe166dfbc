<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.platform.cloud.monitor.dao.ServicesDao">
    <select id="getAllOnlineServices" resultType="com.mega.platform.cloud.data.dto.monitor.ServicesRunningStatusScanDTO">
        SELECT t1.*,
               t2.check_alive_type AS checkAliveType,
               t3.private_ip       AS serverIp,
               t2.name             AS servicesGroupName,
               t4.name             AS projectName
        FROM services AS t1
                 LEFT JOIN services_group AS t2 ON t1.services_group_id = t2.id
                 LEFT JOIN ecs_server AS t3 ON t1.ecs_server_id = t3.id
                 LEFT JOIN project AS t4 ON t2.project_id = t4.id
        WHERE t1.delsign = 0
          AND t1.status = 1
          AND t2.delsign = 0
          AND t2.status = 1
    </select>
    <select id="getServiceParam" resultType="java.lang.String">
        SELECT t1.param_value
        FROM jenkins_job_template_param_value AS t1
                 LEFT JOIN jenkins_job_template_param AS t2 ON t1.jenkins_job_templete_param_id = t2.id
        WHERE t1.services_data_id = #{servicesId}
          AND t1.services_data_type = 2
          AND t2.param_key = #{key}
    </select>
    <select id="getAllOnlineServicesGroup" resultType="com.mega.platform.cloud.data.dto.monitor.ServicesGroupRunningStatusScanDTO">
        SELECT *, t2.name AS projectName
        FROM services_group AS t1
                 LEFT JOIN project AS t2 ON t1.project_id = t2.id
        WHERE t1.status = 1
          AND t1.delsign = 0
    </select>
</mapper>