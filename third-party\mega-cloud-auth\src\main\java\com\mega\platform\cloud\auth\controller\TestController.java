package com.mega.platform.cloud.auth.controller;

import com.mega.platform.cloud.common.constant.JwtConstants;
import com.mega.platform.cloud.common.utils.JwtTokenUtil;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "auth测试接口")
@Slf4j
@RestController
@RequestMapping("/auth/api/test")
public class TestController {
    private final JwtTokenUtil jwtTokenUtil;

    public TestController(JwtTokenUtil jwtTokenUtil) {
        this.jwtTokenUtil = jwtTokenUtil;
    }

    @ApiOperation("测试token")
    @PostMapping("/public/generate/token")
    public Result<?> generateToken() {
        BaseReqVO baseReqVO = new BaseReqVO();
        baseReqVO.setAppId(1L);
        return Results.success(jwtTokenUtil.generateToken(baseReqVO));
    }

    @ApiOperation("测试token")
    @PostMapping("/parse/token")
    public Result<?> parseToken(HttpServletRequest request) {
        log.info(String.valueOf(request.getAttribute(JwtConstants.CLAIM_APP_ID)));
        return Results.success();
    }
}
