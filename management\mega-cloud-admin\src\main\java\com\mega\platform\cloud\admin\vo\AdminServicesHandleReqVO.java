package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel(value = "停止服务请求参数")
public class AdminServicesHandleReqVO {
    @ApiModelProperty(value = "更新服务id", required = true)
    @NotNull
    private Long servicesId;
}
