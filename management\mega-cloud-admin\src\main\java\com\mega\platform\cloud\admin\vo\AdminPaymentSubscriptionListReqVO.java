package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("订阅记录分页查询参数")
public class AdminPaymentSubscriptionListReqVO {
    @ApiModelProperty("订单号")
    private Long orderId;

    @ApiModelProperty("三方平台编码")
    private String platformCode;

    @ApiModelProperty("订阅状态")
    private Integer status;

    @ApiModelProperty("页码，从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty("每页数量，默认50")
    private Integer pageSize = 50;
}
