package com.mega.platform.cloud.common.utils;

import org.springframework.core.env.Environment;

public class EnvUtil {
    private static Environment springEnv;

    public EnvUtil(Environment environment) {
        springEnv = environment;
    }

    public static String getEnv() {
        // 优先从 Spring 环境中获取
        if (springEnv != null) {
            String[] profiles = springEnv.getActiveProfiles();
            if (profiles.length > 0) {
                return profiles[0].toLowerCase();
            }
        }

        // Fallback: 从 JVM 参数或环境变量获取
        return System.getProperty("spring.profiles.active",
                        System.getenv().getOrDefault("SPRING_PROFILES_ACTIVE", "default"))
                .toLowerCase();
    }

    public static boolean isDev() {
        return "dev".equals(getEnv());
    }

    public static boolean isTest() {
        return "test".equals(getEnv());
    }

    public static boolean isProd() {
        return "prod".equals(getEnv());
    }

    public static boolean isLocal() {
        return "local".equals(getEnv());
    }

    public static boolean isNonProd() {
        return !isProd();
    }

    public static boolean isEnv(String targetEnv) {
        return targetEnv != null && getEnv().equalsIgnoreCase(targetEnv);
    }

    public static boolean isIn(String... envList) {
        if (envList == null) return false;
        String env = getEnv();
        for (String e : envList) {
            if (env.equalsIgnoreCase(e)) return true;
        }
        return false;
    }
}
