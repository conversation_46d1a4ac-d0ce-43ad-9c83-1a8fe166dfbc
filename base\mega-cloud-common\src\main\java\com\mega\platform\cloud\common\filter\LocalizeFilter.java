package com.mega.platform.cloud.common.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import java.io.IOException;


@Component("localizeFilter")
@Slf4j
public class LocalizeFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

//        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        String localeStr = servletRequest.getLocale().toString();
        if (localeStr.indexOf("zh_CN") > -1) {
            localeStr = "zh";
        }
//        LocaleThreadLocalContext.set(localeStr);
//        String reqStr = IOUtils.toString(servletRequest.getInputStream());
//        WrappedHttpServletRequest wrappedHttpServletRequest = new WrappedHttpServletRequest((HttpServletRequest) servletRequest, reqStr);
        filterChain.doFilter(servletRequest, servletResponse);
    }
}
