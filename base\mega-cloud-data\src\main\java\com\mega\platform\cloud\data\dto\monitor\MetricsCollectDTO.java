package com.mega.platform.cloud.data.dto.monitor;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class MetricsCollectDTO {
    private String clientIp;
    private List<MetricsDTO> metrics;
    private Integer sourceType;
    private Long servicesId;
    private Long sourceId;
    private Date collectTime;
    private Integer metricsType;
}
