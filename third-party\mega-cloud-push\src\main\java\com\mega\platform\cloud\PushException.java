package com.mega.platform.cloud;


import com.mega.platform.cloud.core.exception.BaseException;

import java.util.Objects;

public class PushException extends BaseException {

    public PushException(Integer code) {
        this(Objects.requireNonNull(PushErrorCode.getExchangeCode(code)));
    }

    public PushException(PushErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public PushException(Integer code, String message) {
        super(code, message);
    }
}
