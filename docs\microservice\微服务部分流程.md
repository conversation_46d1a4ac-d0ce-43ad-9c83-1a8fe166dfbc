## 表结构

### 服务组 services_group

services_update_type 服务组启动方式2004 1-普通重启 2-滚服重启 3-导流重启 4-脚本运行

services_env 服务环境2005 dev | test | beta | prod | ...

services_alive_num 如果是滚服/导流重启 配置需要保活的数量

running_status 服务组运行状态2003 0-未运行 1-运行中 2-构建中 -1-构建失败

status 服务组上下线状态2001 0-下线 1-上线

check_alive_num 保活状态检查2008 

### 服务 services

running_status 服务运行状态2002 0-未运行 1-运行中 2-构建中 3-队列中 4-待关闭 -1-构建失败

status 服务上下线状态2001 0-下线 1-上线

## 流程与方法

### 创建服务组

ServicesService.createServicesGroup

- checkCreateServicesGroupParams 参数检查：projectId内服务组名不能重复
- services_group保存入库
- checkTemplateParamKey 检查模板必要参数是否完全
- jenkins_job_template_param_value保存入库

### 创建服务

ServicesService.createServices

- checkCreateServicesParams 参数检查：servicesGroupId内服务名不能重复，全局不能相同机器相同端口号
- services保存入库
- createJenkinsUser 创建管理员的jenkins用户，如果存在则直接返回，如果不存在则创建jenkins用户，生成apitoken，保存jenkins_user并返回
- createJenkinsSshServer 创建jenkins ssh server，如果存在则直接返回，如果不存在则创建jenkins ssh server，保存jenkins_ssh_server并返回
- createJenkinsView 创建jenkins view，如果存在则直接返回，如果不存在则创建jenkins view，保存jenkins_view并返回
- createJenkinsJob 创建jenkins job，检查这个服务组内有没有相同机器的jenkins job，如果有则直接返回，如果不存在则创建jenkins job，复制当前jenkins job到jenkins view下，保存jenkins_job并返回
- checkTemplateParamKey 检查模板必要参数是否完全
- jenkins_job_template_param_value保存入库
- services更新入库

### 构建服务组

ServicesService.buildServicesGroup

- buildTryLock 上锁 防止并发
- checkServicesGroupCanBuild 状态检查：服务组状态必须是上线，服务组运行状态必须是非构建中
- createJenkinsTaskDTO 根据启动方式，服务真实运行状态，保活数量等 给服务分配真正的任务
    - checkServicesServiceStatus 根据端口号判断服务是否正在运行
    - assignServicesAction 分配服务执行任务
        - 停止操作 - 对正在运行的程序分配停止任务
        - 重启操作
            - 如果是普通重启和脚本运行 - 保活参数没有意义，对正在运行的程序分配重启任务，对没有运行的程序分配启动任务
            - 如果是滚服重启和导流重启
                - 总实例数<保活数 - 抛出异常
                - 总实例数=保活数 - 服务和保活数量必须要大于等于2
                    - 依次重启，优先对没有运行的程序分配启动任务，再对正在运行的程序分配重启任务
                - 总实例数>保活数
                    - 优先对没有运行的程序分配启动任务，直到达到保活数量为止
                    - 如果数量不够，优先对正在运行的程序分配重启任务，直到达到保活数量为止，再对剩余正在运行的程序分配停止任务
- createAndBuildJenkinsTask 创建jenkins_task_group和jenkins_task，修改服务组状态为构建中，修改所有服务状态为队列中
- executeJenkinsTask 异步，调用jenkins接口真正执行任务
    - beforeDoTask 执行前判断 目前主要是导流重启需要通知并等待服务器确定允许关闭，正在检查中的服务运行状态修改成待关闭
    - doTask 调用jenkins接口执行构建任务
    - afterDoTask 执行后判断
        - 脚本执行不需要检查
        - 非脚本执行 stop指令只检查业务是否停止运行，成功停止将服务运行状态修改成未运行。start | restart指令检查服务是否成功运行，还要根据check_consul检查是否成功注册consul，成功启动将服务运行状态修改成已运行
    - 组间并行组内串行，只有完整走完这套流程才会执行下一个，如果有失败的就跳过继续走下一个
    - 全部执行完，判断组内是否还有正在运行的程序服务，修改服务组的运行状态

## 页面设计

### 创建服务组

#### 服务组名称

输入服务组名称

#### Jenkins实例

调用接口获取Jenkins实例列表

#### Jenkins模板

调用接口获取Jenkins模板列表，选择一个模板后，调用接口获取模板参数列表，部分参数有默认值，然后动态加载字段，以输入框的形式输入

#### 发布环境/重启方式

进入系统的时候调用字典值接口，发布环境cate2005，重启方式cate2004

#### 是否需要Consul检查

默认显示，如果重启方式是脚本运行的话隐藏

#### 保活数量

默认隐藏，如果重启方式是滚服重启/导流重启的话显示

#### 模板动态参数

正常填写

### 服务组列表

| 服务组名称 | 上下线状态 status | 运行状态 running_status                | 操作                          |
| ---------- | :---------------- | :------------------------------------- | ----------------------------- |
| 服务组名称 | 0-下线 1-上线     | 0-未运行 1-运行中 2-构建中 -1-构建失败 | 上线/下线 重启 停止 编辑 删除 |

#### 按钮逻辑

上线/下线（status=0/1）：上线服务组显示下线按钮，下线服务组显示上线按钮，只有未运行（running_status=0）的才可以下线

停止（running_status=2，最终变成0/-1）：返回字段（support_stop=0不显示/1显示）控制是否显示停止按钮，只有已上线（status=1）且运行中和构建失败（running_status=1/-1）才可以点

重启（running_status=2，最终变成1/-1）：只有已上线（status=1）且不在构建中（running_status=1/0/-1）才可以点

点击任何按钮返回成功后刷新列表

编辑：调用接口返回服务组及模板信息，返回字段（can_edit=0不可编辑/1可编辑）控制是否所有字段都可编辑

can_edit=1时可以编辑所有字段

can_edit=0时 Jenkins实例，Jenkins模板及参数，发布环境不可编辑

删除（delsign=1）：只有已下线（status=0）的可以删除

### 创建服务

先调用接口获取模板参数列表，流程同服务组

#### 服务名

输入服务名称

#### 模板动态参数

正常填写

### 服务列表

| 服务名称 | 上下线状态 status | 运行状态 running_status                                  | 操作                     |
| -------- | :---------------- | :------------------------------------------------------- | ------------------------ |
| 服务名称 | 0-下线 1-上线     | 0-未运行 1-运行中 2-构建中 3-队列中 4-待停止 -1-构建失败 | 上线/下线 停止 编辑 删除 |

#### 按钮逻辑

上线/下线（status=0下线/1上线）：上线服务显示下线按钮，下线服务显示上线按钮，只有未运行（running_status=0）的才可以下线

停止（running_status=2，最终变成0/-1）：返回字段（support_stop=0不显示/1显示）控制是否显示停止按钮，只有已上线（status=1）且运行中和构建失败（running_status=1/-1）才可以点

点击任何按钮返回成功后刷新列表

编辑：调用接口返回服务组及模板信息，返回字段（can_edit=0不可编辑/1可编辑）控制是否所有字段都可编辑

can_edit=1时可以编辑所有字段

can_edit=0时 服务名称

删除（delsign=1）：只有已下线（status=0）的可以删除