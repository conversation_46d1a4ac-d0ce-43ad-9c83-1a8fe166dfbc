package com.mega.platform.cloud.admin.util;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import io.jsonwebtoken.Claims;

/**
 * AdminCryptUtils 测试类 - 使用main方法启动
 *
 * <AUTHOR>
 */
public class AdminCryptUtilsTest {

    /**
     * 主方法，用于测试AdminCryptUtils.generateSimpleJwtToken方法
     */
    public static void main(String[] args) {
        try {
            new AdminCryptUtilsTest().testGenerateSimpleJwtToken();
        } catch (Exception e) {
            System.err.println("执行测试时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试生成JWT Token并打印结果
     */
    public void testGenerateSimpleJwtToken() {
        System.out.println("开始测试AdminCryptUtils.generateSimpleJwtToken方法...");
        
        try {
            // 测试用例1：普通ID值
            Long adminUserId = 12345L;
            Long tokenVersion = 1001L;
            System.out.println("尝试生成Token，参数：adminUserId=" + adminUserId + ", tokenVersion=" + tokenVersion);
            String token1 = AdminCryptUtils.generateSimpleJwtToken(adminUserId, tokenVersion);
            System.out.println("\n测试用例1 - 普通ID值:");
            System.out.println("adminUserId: " + adminUserId);
            System.out.println("tokenVersion: " + tokenVersion);
            System.out.println("生成的Token: " + token1);
            printTokenInfo(token1);
            
            // 测试用例2：最小值
            Long minAdminUserId = 1L;
            Long minTokenVersion = 1L;
            System.out.println("尝试生成Token，参数：adminUserId=" + minAdminUserId + ", tokenVersion=" + minTokenVersion);
            String token2 = AdminCryptUtils.generateSimpleJwtToken(minAdminUserId, minTokenVersion);
            System.out.println("\n测试用例2 - 最小值:");
            System.out.println("adminUserId: " + minAdminUserId);
            System.out.println("tokenVersion: " + minTokenVersion);
            System.out.println("生成的Token: " + token2);
            printTokenInfo(token2);
            
            // 测试用例3：零值
            Long zeroAdminUserId = 0L;
            Long zeroTokenVersion = 0L;
            System.out.println("尝试生成Token，参数：adminUserId=" + zeroAdminUserId + ", tokenVersion=" + zeroTokenVersion);
            String token3 = AdminCryptUtils.generateSimpleJwtToken(zeroAdminUserId, zeroTokenVersion);
            System.out.println("\n测试用例3 - 零值:");
            System.out.println("adminUserId: " + zeroAdminUserId);
            System.out.println("tokenVersion: " + zeroTokenVersion);
            System.out.println("生成的Token: " + token3);
            printTokenInfo(token3);
            
            System.out.println("\nAdminCryptUtils.generateSimpleJwtToken方法测试完成");
        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getClass().getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 打印Token的基本信息
     */
    private void printTokenInfo(String token) {
        try {
            // 验证token格式
            String[] parts = token.split("\\.");
            System.out.println("Token包含 " + parts.length + " 个部分");
            
            // 解析token并打印Claims
            Claims claims = AdminCryptUtils.parseJwtToken(token);
            System.out.println("Token解析结果:");
            System.out.println("  - adminUserId: " + claims.get(AdminAuthConstant.CONTEXT_ADMIN_USER_ID));
            System.out.println("  - tokenVersion: " + claims.get(AdminAuthConstant.CONTEXT_TOKEN_VERSION));
            System.out.println("  - 签发时间: " + claims.getIssuedAt());
            System.out.println("  - 过期时间: " + claims.getExpiration());
        } catch (Exception e) {
            System.err.println("Token解析失败: " + e.getMessage());
        }
    }
}