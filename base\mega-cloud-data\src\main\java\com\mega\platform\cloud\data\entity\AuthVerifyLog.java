package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "auth_verify_log")
public class AuthVerifyLog {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 应用ID，表示来源应用
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 认证渠道标识，例如：wechat、apple
     */
    @Column(name = "channel_code")
    private String channelCode;

    /**
     * 用于认证的登录ID，如手机号、openid等
     */
    @Column(name = "login_id")
    private String loginId;

    /**
     * 认证结果，例如：success、fail
     */
    @Column(name = "verify_result")
    private String verifyResult;

    /**
     * 客户端IP地址
     */
    @Column(name = "client_ip")
    private String clientIp;

    /**
     * 设备唯一标识，例如设备号、UUID等
     */
    @Column(name = "device_id")
    private String deviceId;

    /**
     * 认证日志创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 记录状态：0-有效，1-无效
     */
    @Column(name = "delsign")
    private Byte delsign;

    /**
     * 错误信息，认证失败时记录原因
     */
    @Column(name = "error_msg")
    private String errorMsg;
}