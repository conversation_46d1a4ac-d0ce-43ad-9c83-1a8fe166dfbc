package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_app_config")
public class PaymentAppConfig {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Integer id;

    /**
     * 应用ID
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 认证渠道ID，关联third_platform表
     */
    @Column(name = "third_platform_id")
    private Long thirdPlatformId;

    /**
     * 平台配置信息（JSON格式）
     */
    @Column(name = "config")
    private String config;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}