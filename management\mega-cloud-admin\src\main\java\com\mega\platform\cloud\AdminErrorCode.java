package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum AdminErrorCode {

    ERR_0(0), // 未知错误
    SUCCESS(1), // 成功
    PARAM_ERROR(4000), // 参数错误
    TOKEN_EXPIRED(4001), // token失效
    ACCESS_DENIED(4003), // 没有权限
    ;
    private final Integer code;

    AdminErrorCode(Integer code) {
        this.code = code;
    }

    public static AdminErrorCode getExchangeCode(Integer code) {
        for (AdminErrorCode exchangeCode : AdminErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
