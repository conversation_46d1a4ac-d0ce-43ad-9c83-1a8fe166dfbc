CREATE TABLE `push_plan` (
  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '推送计划ID',
  `title` VARCHAR(255) NOT NULL COMMENT '推送标题',
  `content` TEXT NOT NULL COMMENT '推送内容',
  `push_type` TINYINT NOT NULL DEFAULT 0 COMMENT '推送类型: 0=全服推送, 1=注册7天用户推送',
  `plan_time` TIMESTAMP NOT NULL COMMENT '计划推送时间',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '推送状态: 0=待推送, 1=推送中, 2=推送完成, 3=推送失败',
  `complete_time` TIMESTAMP NULL COMMENT '推送完成时间',
  `total_count` INT NOT NULL DEFAULT 0 COMMENT '计划推送总数量',
  `completed_count` INT NOT NULL DEFAULT 0 COMMENT '已完成推送数量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_plan_time` (`plan_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推送计划表';