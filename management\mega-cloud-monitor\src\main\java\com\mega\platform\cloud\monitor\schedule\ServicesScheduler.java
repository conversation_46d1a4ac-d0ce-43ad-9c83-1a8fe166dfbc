package com.mega.platform.cloud.monitor.schedule;

import com.mega.platform.cloud.data.dto.monitor.ServicesGroupRunningStatusScanDTO;
import com.mega.platform.cloud.data.dto.monitor.ServicesRunningStatusScanDTO;
import com.mega.platform.cloud.monitor.dao.ServicesDao;
import com.mega.platform.cloud.monitor.service.services.ServicesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.mega.platform.cloud.common.constant.MicroserviceConstants.JENKINS_TEMPLATE_KEY_PORT;

@Service
@Slf4j
public class ServicesScheduler {

    private final ServicesDao servicesDao;
    private final ServicesService servicesService;

    @Autowired
    public ServicesScheduler(ServicesDao servicesDao, ServicesService servicesService) {
        this.servicesDao = servicesDao;
        this.servicesService = servicesService;
    }

    @Scheduled(fixedDelay = 10 * 1000)
    public void runningStatusScan() {
        List<ServicesRunningStatusScanDTO> servicesList = servicesDao.getAllOnlineServices();
        for (ServicesRunningStatusScanDTO services : servicesList) {
            try {
                String port = servicesDao.getServiceParam(services.getId(), JENKINS_TEMPLATE_KEY_PORT);
                services.setPort(Integer.valueOf(port));
                servicesService.checkServicesRunningStatus(services);
            } catch (Exception e) {
                log.error("runningStatusScan checkServicesRunningStatus error, services: {}", services, e);
            }
        }
        List<ServicesGroupRunningStatusScanDTO> servicesGroupList = servicesDao.getAllOnlineServicesGroup();
        for (ServicesGroupRunningStatusScanDTO servicesGroup : servicesGroupList) {
            try {
                servicesService.checkServicesGroupRunningStatus(servicesGroup);
            } catch (Exception e) {
                log.error("runningStatusScan checkServicesGroupRunningStatus error, servicesGroup: {}", servicesGroup, e);
            }
        }
        log.info("runningStatusScan success");
    }
}
