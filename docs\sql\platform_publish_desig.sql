-- --------------------------------------------------------
-- Table structure for  平台发型商相关
-- --------------------------------------------------------
CREATE TABLE `project_channel` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '渠道ID',
  `name` VARCHAR(100) NOT NULL COMMENT '渠道名称(如project Store, Google Play)',
  `device_os_id` INT NOT NULL COMMENT '平台类型操作系统',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0=禁用, 1=启用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='project发行渠道表';

CREATE TABLE `project_publisher` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '发行商ID',
  `name` VARCHAR(100) NOT NULL COMMENT '发行商名称',
  `contact` VARCHAR(100) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `website` VARCHAR(255) COMMENT '发行商官网',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 0=禁用, 1=启用',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='project发行商表';

CREATE TABLE `project_distribution` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `project_id` INT NOT NULL COMMENT '项目ID',
  `project_channel_id` INT NOT NULL COMMENT '渠道ID',
  `project_publisher_id` INT COMMENT '发行商ID(可为空，表示自发行)',
  `release_status` TINYINT NOT NULL DEFAULT 0 COMMENT '发布状态: 0=未发布, 1=已发布, 2=已下架',
  `release_time` DATETIME COMMENT '发布时间',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_project_channel` (`project_id`, `project_channel_id`),
  KEY `idx_channel` (`project_channel_id`),
  KEY `idx_publisher` (`project_publisher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='project发行关系表';
