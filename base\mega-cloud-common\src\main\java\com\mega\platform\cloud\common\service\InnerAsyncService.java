package com.mega.platform.cloud.common.service;

import com.mega.platform.cloud.common.CommonConfig;
import com.mega.platform.cloud.core.utils.DateUtils;
import com.mega.platform.cloud.data.annotation.FeishuParam;
import com.mega.platform.cloud.data.dto.common.feishu.BaseFeishuDTO;
import com.mega.platform.cloud.data.dto.common.mail.BaseMailDTO;
import com.mega.platform.cloud.data.dto.common.feishu.FeishuMessage;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.mail.internet.MimeMessage;
import java.io.File;
import java.lang.reflect.Field;
import java.util.*;

@Service
@Slf4j
public class InnerAsyncService {
    private final RestTemplate restTemplate;
    @Value("${spring.mail.username}")
    private String from;
    private final JavaMailSender javaMailSender;

    @Autowired
    public InnerAsyncService(RestTemplate restTemplate, JavaMailSender javaMailSender) {
        this.restTemplate = restTemplate;
        this.javaMailSender = javaMailSender;
    }

    @Async("taskExecutor")
    public void sendFeishuMessageAsync(BaseFeishuDTO dto) {
        List<FeishuTextLevel> texts = convertFeishuText(dto);

        FeishuMessage feishuMessage = new FeishuMessage();
        FeishuMessage.Card card = new FeishuMessage.Card();
        List<FeishuMessage.Card.Elements> elements = new ArrayList<>();

        for (FeishuTextLevel text : texts) {
            if (text.getText() == null || text.getText().isEmpty()) {
                continue;
            }
            FeishuMessage.Card.Elements element = new FeishuMessage.Card.Elements();
            FeishuMessage.Card.Elements.Text elementText = new FeishuMessage.Card.Elements.Text();
            elementText.setContent(text.getText());
            if (text.getLevel() != 0) {
                elementText.setTextSize(dto.getLevelFont(text.getLevel()));
            }
            element.setText(elementText);
            elements.add(element);
        }
        card.setElements(elements);


        FeishuMessage.Card.Header header = new FeishuMessage.Card.Header();
        FeishuMessage.Card.Header.Title headerTitle = new FeishuMessage.Card.Header.Title();
        headerTitle.setContent(dto.getTitle());
        header.setTitle(headerTitle);
        card.setHeader(header);
        feishuMessage.setCard(card);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<FeishuMessage> requestEntity = new HttpEntity<>(feishuMessage, headers);
        for (String botUrl : dto.getBotUrls()) {
            String result = this.restTemplate.postForObject(botUrl, requestEntity, String.class);
            log.info("result:" + result);
        }
    }

    public List<FeishuTextLevel> convertFeishuText(BaseFeishuDTO dto) {
        if (dto == null) return null;
        Map<String, String> map = new LinkedHashMap<>();
        Class<?> clazz = dto.getClass();
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                FeishuParam annotation = field.getAnnotation(FeishuParam.class);
                if (annotation != null) {
                    field.setAccessible(true);
                    try {
                        Object value = field.get(dto);
                        if (value != null) {
                            map.put(annotation.value(), value != null ? value.toString() : "");
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace(); // 可加日志
                    }
                }
            }
            clazz = clazz.getSuperclass(); // 支持多级继承
        }
        dto.setDataMap(map);
        dto.addTag(CommonConfig.ENV + "环境");

        List<FeishuTextLevel> results = new ArrayList<>();
        // 主表单
        StringBuilder sb = new StringBuilder();

        // 1. 遍历 dataMap 构造主体内容
        dto.getDataMap().forEach((key, value) -> {
            sb.append("**").append(key).append("：** ").append(value).append("\n");
        });

        if (dto.getAt() != null) {
            for (String at : dto.getAt()) {
                sb.append("<at id=").append(at).append("></at>\n");
            }
        }
        results.add(new FeishuTextLevel().setLevel(0).setText(sb.toString()));
        // emoji
        results.add(new FeishuTextLevel().setLevel(3).setText(dto.getEmoJi()));

        // 优先级与tag
        StringBuilder sb3 = new StringBuilder();
        // 添加优先级
        if (dto.getLevel() != null && dto.getLevel() != 0) {
            sb3.append(dto.getLevelTag(dto.getLevel())).append("\n");
        }
        // 2. 添加统计时间
        String now = DateUtils.formatTime(new Date()); // 你已有该工具类
        sb3.append("<text_tag color='").append(dto.getTagColor(dto.getLevel())).append("'>时间：").append(now).append("</text_tag>\n");

        // 3. 添加 tags + 环境名
        sb3.append("<text_tag color='" + dto.getTagColor(dto.getLevel()) + "'>");
        if (dto.getTags() != null) {
            for (String tag : dto.getTags()) {
                sb3.append(" [").append(tag).append("]");
            }
        }
        sb3.append("</text_tag>\n");
        results.add(new FeishuTextLevel().setLevel(dto.getLevel()).setText(sb3.toString()));
        return results;
    }

    @Data
    @Accessors(chain = true)
    public static class FeishuTextLevel {
        private Integer level;
        private String text;
    }

    @Async("taskExecutor")
    public void sendMailAsync(BaseMailDTO dto) throws Exception {
        MimeMessage message = javaMailSender.createMimeMessage();
        // true = 支持 multipart（HTML + 附件）
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(from);
        helper.setTo(dto.getTo().toArray(String[]::new));
        helper.setSubject(dto.getTitle());
        helper.setText(dto.getContent(), true); // true 表示 HTML 内容
        File attachment = dto.getAttachment();
        if (attachment != null) {
            FileSystemResource file = new FileSystemResource(attachment);
            helper.addAttachment(attachment.getName(), file);
        }
        javaMailSender.send(message);
        log.info("邮件发送成功");
    }

}
