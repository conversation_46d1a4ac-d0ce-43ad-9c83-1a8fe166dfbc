# FeignClient扫描功能说明

## 功能描述

在mega-cloud-admin模组中新增了FeignClient接口扫描功能，该功能会在程序启动时自动扫描所有带有`@FeignClient`注解的接口，提取接口上的`@RequestMapping`注解和方法上的`@PostMapping`注解，拼接完整的API路径并打印到日志中。

## 实现原理

1. **启动监听**: 使用`ApplicationListener<ApplicationReadyEvent>`监听应用启动完成事件
2. **接口扫描**: 使用`ClassPathScanningCandidateComponentProvider`扫描`com.mega.platform.cloud.client`包下的所有类
3. **注解过滤**: 通过`AnnotationTypeFilter`过滤出带有`@FeignClient`注解的接口
4. **路径提取**: 使用反射获取`@RequestMapping`和`@PostMapping`注解的值
5. **路径拼接**: 将基础路径和方法路径拼接成完整的API路径
6. **日志输出**: 将扫描结果输出到日志中

## 代码结构

### 核心类
- `FeignClientScannerListener`: FeignClient接口扫描监听器
- `TestFeignScanController`: 测试Controller（可选）

### 依赖配置
- 在`pom.xml`中添加了`mega-cloud-client`依赖
- 在`AdminApplication.java`中添加了`@EnableFeignClients`注解

## 使用示例

### FeignClient接口示例
```java
@FeignClient(value = "mega-cloud-access-" + "${spring.profiles.active}", contextId = "mega-cloud-access-app-client")
@RequestMapping("/access/api/app")
public interface AccessAppClient {
    @ApiOperation("获取登录token")
    @PostMapping("/public/token")
    Result<AccessAppTokenRespVO> accessAppToken(@Validated @RequestBody AccessAppTokenReqVO vo);
}
```

### 输出结果
```
2024-07-18 11:50:00.123 INFO  --- FeignClient [AccessAppClient] 方法 [accessAppToken] 完整路径: /access/api/app/public/token
```

## 日志输出格式

启动时会输出以下格式的日志：
```
INFO  --- 开始扫描FeignClient接口...
INFO  --- 发现FeignClient接口: com.mega.platform.cloud.client.access.AccessAppClient
INFO  --- FeignClient [AccessAppClient] 方法 [accessAppToken] 完整路径: /access/api/app/public/token
INFO  --- 发现FeignClient接口: com.mega.platform.cloud.client.auth.AuthVerificationClient
INFO  --- FeignClient [AuthVerificationClient] 方法 [sendSmsCode] 完整路径: /auth/api/verification/sms/code
INFO  --- FeignClient [AuthVerificationClient] 方法 [verifySmsCode] 完整路径: /auth/api/verification/sms/verify
INFO  --- FeignClient扫描完成
```

## 测试方法

1. 启动mega-cloud-admin应用
2. 查看启动日志，确认FeignClient扫描功能正常工作
3. 可选：调用测试接口 `POST /admin/api/test/feign-scan` 验证功能

## 注意事项

1. 该功能只扫描`com.mega.platform.cloud.client`包下的FeignClient接口
2. 只处理带有`@PostMapping`注解的方法
3. 路径拼接会自动处理斜杠，确保格式正确
4. 如果注解中同时有`value`和`path`属性，优先使用`value`属性

## 扩展说明

如需扫描其他HTTP方法（如GET、PUT、DELETE等），可以在`FeignClientScannerListener`中添加相应的注解处理逻辑。