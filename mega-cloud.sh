#!/bin/bash

APP_NAME="mega-cloud"

usage() {
    echo "Usage: [start|restart|stop]"
    exit 1
}

start() {
    config="--server.port=${3} --spring.profiles.active=${2} --mega.platform.services-id=${7}"
    # 如果 ${2} 是 test，则追加 JIT 编译相关参数
    if [ "${2}" = "test" ]; then
        config="${config} -XX:-CICompilerCountPerCPU -XX:CICompilerCount=1"
    fi
    docker pull "${6}"/${APP_NAME}/${APP_NAME}-"${1}"/"${4}"
    docker run \
    --name "${APP_NAME}-${1}-${2}-${3}" \
    -h "${1}-${2}-${3}" \
    -p "${3}:${3}" \
    -e MEM="${5}" \
    -e CONFIG="${config}" \
    -v /opt/log/${APP_NAME}/"${1}":/opt/log \
    -d "${6}"/${APP_NAME}/${APP_NAME}-"${1}"/"${4}"
}

restart() {
   stop "$1" "$2" "$3"
   start "$1" "$2" "$3" "$4" "$5" "$6" "$7"
}

stop() {
    docker stop "${APP_NAME}-${1}-${2}-${3}"
    docker container rm "${APP_NAME}-${1}-${2}-${3}"
    docker system prune -af
}

case "$1" in
start)
    start "$2" "$3" "$4" "$5" "$6" "$7" "$8"
    ;;
restart)
    restart "$2" "$3" "$4" "$5" "$6" "$7" "$8"
    ;;
stop)
    stop "$2" "$3" "$4"
    ;;
*)
    usage
    ;;
esac
