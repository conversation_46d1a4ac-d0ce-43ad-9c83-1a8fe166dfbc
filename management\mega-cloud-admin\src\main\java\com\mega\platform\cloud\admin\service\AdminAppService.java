package com.mega.platform.cloud.admin.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dao.AdminAppDao;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.AuthAppConfigMapper;
import com.mega.platform.cloud.core.PageResult;

import com.mega.platform.cloud.data.entity.AuthAppConfig;
import com.mega.platform.cloud.data.entity.ProjectApp;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 应用管理服务层
 */
@Service
@Slf4j
public class AdminAppService {
    
    private final AdminAppDao adminAppDao;

    @Autowired
    public AdminAppService(AdminAppDao adminAppDao) {
        this.adminAppDao = adminAppDao;
    }
    
    /**
     * 分页查询应用列表
     */
    public PageResult<AdminAppRespVO> findAppList(Long projectId, AdminAppListReqVO reqVO) {
        // 参数校验
        Integer pageNum = reqVO.getPageNum();
        Integer pageSize = reqVO.getPageSize();
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1 || pageSize > 100) {
            pageSize = 20;
        }
        
        // 查询总数
        Long total = adminAppDao.countAppList(projectId, reqVO.getName(), reqVO.getStatus());
        if (total == 0) {
            PageResult<AdminAppRespVO> result = new PageResult<>();
            result.setTotal(0L);
            result.setList(null);
            return result;
        }
        
        // 分页查询数据
        Integer offset = (pageNum - 1) * pageSize;
        List<ProjectApp> appList = adminAppDao.selectAppList(projectId, offset, pageSize, reqVO.getName(), reqVO.getStatus());
        
        // 转换为VO
        List<AdminAppRespVO> voList = appList.stream().map(this::convertToRespVO).collect(Collectors.toList());
        
        PageResult<AdminAppRespVO> result = new PageResult<>();
        result.setTotal(total);
        result.setList(voList);
        return result;
    }
    /**
     * 创建应用
     */
    @Transactional(rollbackFor = Exception.class)
    public AdminAppRespVO createApp(Long projectId, AdminAppCreateReqVO reqVO) {
        // 参数校验
        if (!StringUtils.hasText(reqVO.getName())) {
            throw new IllegalArgumentException("应用名称不能为空");
        }
        
        // 检查应用名称是否重复
        ProjectApp existApp = adminAppDao.selectAppByProjectIdAndName(projectId, reqVO.getName(), null);
        if (existApp != null) {
            throw new IllegalArgumentException("应用名称已存在");
        }
        
        // 创建应用
        ProjectApp app = new ProjectApp();
        // 生成app key 
        String appKey = generateAppKey();
        String appSecret = generateAppSecret();
        // 生成app secrect
        app.setAppKey(appKey)
           .setAppSecret(appSecret)
           .setName(reqVO.getName())
           .setProjectId(projectId)
           .setStatus(1)
           .setRemark(reqVO.getRemark())
           .setCreateTime(new Date())
           .setUpdateTime(new Date())
           .setDelsign((byte) 0);
        
        int result = adminAppDao.insertSelective(app);
        if (result <= 0) {
            throw new RuntimeException("创建应用失败");
        }
        
        return convertToRespVO(app);
    }
    
    /**
     * 获取应用详情
     */
    public AdminAppRespVO getAppDetail(Long projectId, AdminAppDetailReqVO reqVO) {
        if (reqVO.getId() == null) {
            throw new IllegalArgumentException("应用ID不能为空");
        }
        
        ProjectApp app = adminAppDao.selectAppByProjectIdAndId(projectId, reqVO.getId());
        if (app == null) {
            throw new IllegalArgumentException("应用不存在");
        }
        
        return convertToRespVO(app);
    }
    
    /**
     * 编辑应用
     */
    @Transactional(rollbackFor = Exception.class)
    public AdminAppRespVO editApp(Long projectId, AdminAppEditReqVO reqVO) {
        // 参数校验
        if (reqVO.getId() == null) {
            throw new IllegalArgumentException("应用ID不能为空");
        }
        
        // 检查应用是否存在
        ProjectApp existApp = adminAppDao.selectAppByProjectIdAndId(projectId, reqVO.getId());
        if (existApp == null) {
            throw new IllegalArgumentException("应用不存在");
        }
        
        // 更新应用
        existApp.setName(reqVO.getName() != null ? reqVO.getName() : existApp.getName())
                .setStatus(reqVO.getStatus() != null ? reqVO.getStatus() : existApp.getStatus())
                .setRemark(reqVO.getRemark() != null ? reqVO.getRemark() : existApp.getRemark())
                .setUpdateTime(new Date());
        
        int result = adminAppDao.updateByPrimaryKeySelective(existApp);
        if (result <= 0) {
            throw new RuntimeException("编辑应用失败");
        }
        
        return convertToRespVO(existApp);
    }
    
    /**
     * 删除应用
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteApp(Long projectId, AdminAppDeleteReqVO reqVO) {
        if (reqVO.getId() == null) {
            throw new IllegalArgumentException("应用ID不能为空");
        }
        
        // 检查应用是否存在
        ProjectApp existApp = adminAppDao.selectAppByProjectIdAndId(projectId, reqVO.getId());
        if (existApp == null) {
            throw new IllegalArgumentException("应用不存在");
        }
        
        // 软删除
        existApp.setDelsign((byte) 1)
                .setUpdateTime(new Date());
        
        int result = adminAppDao.updateByPrimaryKeySelective(existApp);
        if (result <= 0) {
            throw new RuntimeException("删除应用失败");
        }
    }
    
    /**
     * 转换为响应VO
     */
    private AdminAppRespVO convertToRespVO(ProjectApp app) {
        AdminAppRespVO vo = new AdminAppRespVO();
        BeanUtils.copyProperties(app, vo);
        return vo;
    }
    
    /**
     * 生成应用Key
     */
    private String generateAppKey() {
        return "app_" + System.currentTimeMillis()/1000;
    }
    
    /**
     * 生成应用密钥
     */
    private String generateAppSecret() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}