package com.mega.platform.cloud.data.vo.access;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@ApiModel("合作方License验证请求参数")
public class AccessPartnerLicenseReqVO {
    @ApiModelProperty(value = "appKey", example = "app_1752202145", required = true)
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    @ApiModelProperty(value = "服务名称", example = "gossipharbor-cloud-exchange", required = true)
    @NotBlank(message = "服务名称不能为空")
    private String serverName;

    @ApiModelProperty(value = "内网IP地址", example = "*************", required = true)
    @NotBlank(message = "内网IP不能为空")
    private String innerIp;

    @ApiModelProperty(value = "公网IP地址", example = "************", required = true)
    @NotBlank(message = "公网IP不能为空")
    private String publicIp;

    @ApiModelProperty(value = "机器唯一标识", example = "machine-001", required = true)
    @NotBlank(message = "机器ID不能为空")
    private String machineId;

    @ApiModelProperty(value = "License授权码", example = "LICENSE-123456", required = true)
    @NotBlank(message = "License不能为空")
    private String license;
}
