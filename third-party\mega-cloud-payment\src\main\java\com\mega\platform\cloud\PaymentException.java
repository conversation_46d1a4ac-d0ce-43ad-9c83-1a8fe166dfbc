package com.mega.platform.cloud;


import com.mega.platform.cloud.core.exception.BaseException;

import java.util.Objects;

public class PaymentException extends BaseException {

    public PaymentException(Integer code) {
        this(Objects.requireNonNull(PaymentErrorCode.getExchangeCode(code)));
    }

    public PaymentException(PaymentErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public PaymentException(Integer code, String message) {
        super(code, message);
    }
}
