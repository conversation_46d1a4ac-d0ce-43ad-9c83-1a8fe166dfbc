<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建服务组</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        h2 {
            text-align: center;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .form-group {
            margin-bottom: 20px;
        }
        #javaParamsDialog {
            display: none;
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
        }
    </style>
    <script>
        // 监听选择变化，更新表单显示
        function updateForm() {
            var jenkinsInstance = document.getElementById("jenkinsInstance").value;
            var env = document.getElementById("env").value;
            var restartType = document.getElementById("restartType").value;
            var keepAlive = document.getElementById("keepAlive");
            var keepAliveLabel = document.getElementById("keepAliveLabel");
            var consulCheck = document.getElementById("consulCheck");
            var jenkinsTemplate = document.getElementById("jenkinsTemplate").value;
            var submitButton = document.getElementById("submitButton");

            // 如果选择的是导流重启或滚服重启，则显示保活数量输入框和标签，否则隐藏
            if (restartType === "drainRestart" || restartType === "rollingRestart") {
                keepAlive.style.display = "inline-block";  // 显示输入框
                keepAliveLabel.style.display = "inline";   // 显示标签
            } else {
                keepAlive.style.display = "none";  // 隐藏输入框
                keepAliveLabel.style.display = "none";   // 隐藏标签
            }

            // 如果选择的是 Java Cloud通用 模板，则显示额外的弹窗输入框
            if (jenkinsTemplate === "javaCloud") {
                document.getElementById("javaParamsDialog").style.display = "block";  // 显示弹窗
            } else {
                document.getElementById("javaParamsDialog").style.display = "none";  // 隐藏弹窗
            }

            // 控制提交按钮的可用性
            if (jenkinsInstance && env && restartType && (restartType !== "drainRestart" && restartType !== "rollingRestart" || keepAlive.value)) {
                submitButton.disabled = false;
            } else {
                submitButton.disabled = true;
            }
        }

        // 提交表单时，可以进一步获取并使用用户在弹窗中输入的值
        function handleSubmit() {
            var param1 = document.getElementById("param1").value;
            var param2 = document.getElementById("param2").value;
            var param3 = document.getElementById("param3").value;

            // 在这里你可以对 param1, param2, param3 进行处理
            console.log("提交的参数：", param1, param2, param3);
        }
    </script>
</head>
<body>
<div class="container">
    <h2>创建服务组</h2>
    <form action="#" method="post" onsubmit="handleSubmit()">
        <!-- 服务组名字 输入框 -->
        <div class="form-group">
            <label for="serviceGroupName">服务组名称:</label>
            <input type="text" id="serviceGroupName" name="serviceGroupName" placeholder="请输入服务组名称" required>
        </div>

        <!-- Jenkins 实例 下拉列表 -->
        <div class="form-group">
            <label for="jenkinsInstance">Jenkins 实例:</label>
            <select id="jenkinsInstance" name="jenkinsInstance" onchange="updateForm()">
                <option value="">请选择 Jenkins 实例</option>
                <option value="test1">test1</option>
                <option value="test2">test2</option>
            </select>
        </div>

        <!-- Jenkins 模板 下拉列表 -->
        <div class="form-group">
            <label for="jenkinsTemplate">Jenkins 模板:</label>
            <select id="jenkinsTemplate" name="jenkinsTemplate" onchange="updateForm()">
                <option value="">请选择 Jenkins 模板</option>
                <option value="javaCloud">Java Cloud通用</option>
                <option value="pythonCloud">Python通用</option>
            </select>
        </div>

        <!-- 发布环境 下拉列表 -->
        <div class="form-group">
            <label for="env">发布环境:</label>
            <select id="env" name="env" onchange="updateForm()">
                <option value="">请选择发布环境</option>
                <option value="dev">dev</option>
                <option value="test">test</option>
                <option value="beta">beta</option>
                <option value="prod">prod</option>
            </select>
        </div>

        <!-- 重启方式 下拉列表 -->
        <div class="form-group">
            <label for="restartType">重启方式:</label>
            <select id="restartType" name="restartType" onchange="updateForm()">
                <option value="">请选择重启方式</option>
                <option value="normalRestart">普通重启</option>
                <option value="rollingRestart">滚服重启</option>
                <option value="drainRestart">导流重启</option>
                <option value="scriptRestart">脚本运行</option>
            </select>
        </div>

        <!-- 保活数量 输入框，默认隐藏 -->
        <div class="form-group">
            <label for="keepAlive" id="keepAliveLabel" style="display:none;">保活数量:</label>
            <input type="number" id="keepAlive" name="keepAlive" placeholder="请输入保活数量" style="display:none;" onchange="updateForm()">
        </div>

        <!-- Consul 检查 单选框 -->
        <div class="form-group">
            <label>是否需要 Consul 检查:</label><br>
            <input type="radio" id="consulCheckYes" name="consulCheck" value="yes" checked>
            <label for="consulCheckYes">是</label><br>
            <input type="radio" id="consulCheckNo" name="consulCheck" value="no">
            <label for="consulCheckNo">否</label><br><br>
        </div>

        <!-- Java Cloud通用 参数 弹窗 -->
        <div id="javaParamsDialog">
            <h3>请输入参数:</h3>
            <div class="form-group">
                <label for="param1">Param1:</label>
                <input type="text" id="param1" name="param1" value="value1"> <!-- 默认值设置为 value1 -->
            </div>
            <div class="form-group">
                <label for="param2">Param2:</label>
                <input type="text" id="param2" name="param2">
            </div>
            <div class="form-group">
                <label for="param3">Param3:</label>
                <input type="text" id="param3" name="param3">
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="form-group">
            <button type="submit" id="submitButton" disabled>提交</button>
        </div>
    </form>
</div>
</body>
</html>
