package com.mega.platform.cloud.client.microservice;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupRespVO;
import com.mega.platform.cloud.data.vo.payment.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;

@FeignClient(value = "mega-cloud-microservice-" + "${spring.profiles.active}", contextId = "mega-cloud-microservice-servicesGroup-client")
@Api(tags = "服务组接口")
public interface ServicesGroupClient {
    @ApiOperation("创建服务组")
    @PostMapping("/microservice/api/servicesGroup/create")
    public Result<CreateServicesGroupRespVO> createServicesGroup(@Validated @RequestBody CreateServicesGroupReqVO vo) throws Exception;

    @ApiOperation("构建服务组")
    @PostMapping("/microservice/api/servicesGroup/build")
    public Result<?> buildServicesGroup(@Validated @RequestBody BuildServicesGroupReqVO vo) throws Exception;
}
