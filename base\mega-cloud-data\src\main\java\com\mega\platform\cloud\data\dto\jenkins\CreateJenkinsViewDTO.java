package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreateJenkinsViewDTO {
    private final static String groovyScriptFormat = "import hudson.model.*\n" +
            "import jenkins.model.*\n" +
            "\n" +
            "def viewName = \"%s\"\n" +
            "\n" + "// 检查视图是否已存在\n" +
            "if (Jenkins.instance.getView(viewName) == null) {\n" +
            "    def v = new ListView(viewName, Jenkins.instance)\n" +
            "    Jenkins.instance.addView(v)\n" +
            "    println \"View '${viewName}' created!\"\n" +
            "} else {\n" +
            "    println \"View '${viewName}' already exists.\"\n"
            + "}";

    private String viewName;

//    public CreateJenkinsViewDTO(String jenkinsUrl, String jenkinsUserName, String jenkinsUserToken, String viewName) {
//        this.viewName = viewName;
//        super.setJenkinsUserName(jenkinsUserName);
//        super.setJenkinsUserToken(jenkinsUserToken);
//        super.setJenkinsUrl(jenkinsUrl);
//    }

    public String getGroovyScriptStr() {
        return String.format(groovyScriptFormat, viewName);
    }

}
