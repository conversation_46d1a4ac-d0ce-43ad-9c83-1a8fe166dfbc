package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "admin_role_router_binding")
public class AdminRoleRouterBinding {
    /**
     * 绑定ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 角色ID
     */
    @Column(name = "admin_role_id")
    private Long adminRoleId;

    /**
     * 路由ID
     */
    @Column(name = "admin_router_id")
    private Long adminRouterId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 状态: 0=正常, 1=禁用
     */
    @Column(name = "delsign")
    private Boolean delsign;
}