package com.mega.platform.cloud.microservice.service;

import com.mega.platform.cloud.common.service.CommonFeishuService;
import com.mega.platform.cloud.common.service.CommonMailService;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesGroupReqVO;
import com.mega.platform.cloud.data.vo.microservice.CreateServicesReqVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class ServicesServiceTest {

    private final ServicesService servicesService;
    private final JenkinsService jenkinsService;
    private final MicroserviceCommonService microserviceCommonService;
    private final CommonFeishuService commonFeishuService;
    private final CommonMailService commonMailService;

    @Autowired
    public ServicesServiceTest(ServicesService servicesService, JenkinsService jenkinsService, MicroserviceCommonService microserviceCommonService, CommonFeishuService commonFeishuService, CommonMailService commonMailService) {
        this.servicesService = servicesService;
        this.jenkinsService = jenkinsService;
        this.microserviceCommonService = microserviceCommonService;
        this.commonFeishuService = commonFeishuService;
        this.commonMailService = commonMailService;
    }

    @Test
    public void testCreateServiceGroup() throws Exception {
        CreateServicesGroupReqVO createServicesGroupReqVO = new CreateServicesGroupReqVO();
        Map<String, String> map = new HashMap<>();
        // ------------------------------
        String moduleName = "account";
        map.put("moduleName", moduleName);
        map.put("gitUrl", "http://gitlab.53site.com/mega-game/game-services/gossipharbor-cloud.git");
        map.put("gitBranch", "master");
        map.put("cloudRestartFileName", "gossipharbor-cloud.sh");
        map.put("appName", "gossipharbor-cloud");
        map.put("groupId", "com.mega.gossipharbor.cloud");
        map.put("gameRegionId", "1001");
        createServicesGroupReqVO.setServicesGroupName("gossipharbor-cloud-" + moduleName);
        createServicesGroupReqVO.setJenkinsTemplateId(2L);
        // ------------------------------
        // ------------------------------
//        String moduleName = "auth";
//        map.put("moduleName", moduleName);
//        map.put("gitUrl", "http://gitlab.53site.com/WerewolfServer/Services/mega-cloud.git");
//        map.put("gitBranch", "master");
//        map.put("cloudRestartFileName", "mega-cloud.sh");
//        map.put("appName", "mega-cloud");
//        map.put("groupId", "com.mega.platform.cloud");
//        createServicesGroupReqVO.setServicesGroupName("platform-" + moduleName);
//        createServicesGroupReqVO.setJenkinsTemplateId(1L);
        // ------------------------------
        createServicesGroupReqVO.setJenkinsParams(map);
        createServicesGroupReqVO.setAdminUserId(1L);
        createServicesGroupReqVO.setJenkinsServiceId(1L);
        createServicesGroupReqVO.setProjectId(1L);
        createServicesGroupReqVO.setProjectAppId(1L);
        createServicesGroupReqVO.setServiceEnv("test");
        createServicesGroupReqVO.setServiceUpdateId(1);
        createServicesGroupReqVO.setServiceAliveNum(1);
        createServicesGroupReqVO.setCheckAliveType(1);
        servicesService.createServicesGroup(createServicesGroupReqVO);
        System.out.println("testCreateServiceGroup");
    }

    @Test
    public void testCreateService() throws Exception {
        CreateServicesReqVO createServicesReqVO = new CreateServicesReqVO();
        Map<String, String> map = new HashMap<>();
        // ------------------------------
        map.put("port", "8931");
        createServicesReqVO.setServiceGroupId(22L);
        createServicesReqVO.setServicesName("gossipharbor-account");
        map.put("gameRegionId", "1001");
        // ------------------------------
        map.put("mem", "256m");
        createServicesReqVO.setTargetEcsServerId(1L);
        createServicesReqVO.setJenkinsParams(map);
        createServicesReqVO.setAdminUserId(1L);
        servicesService.createServices(createServicesReqVO);
        System.out.println("testCreateService");
    }

    @Test
    public void test() throws Exception {
        Thread.sleep(10000);
    }
}
