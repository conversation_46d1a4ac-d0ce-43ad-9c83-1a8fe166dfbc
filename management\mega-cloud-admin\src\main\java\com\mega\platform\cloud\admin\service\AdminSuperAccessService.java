package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dao.AdminSuperAccessDao;
import com.mega.platform.cloud.admin.dto.AdminRoleProjectBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminRoleRouterBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminUserProjectBindingDTO;
import com.mega.platform.cloud.admin.vo.AdminAccessRoleRouterBindingListRespVO;
import com.mega.platform.cloud.data.entity.AdminRoleProjectBinding;
import com.mega.platform.cloud.data.entity.AdminRoleRouterBinding;
import com.mega.platform.cloud.data.entity.AdminRouter;
import com.mega.platform.cloud.data.entity.AdminUserProjectBinding;
import com.mega.platform.cloud.data.entity.AdminUserRouterBinding;
import com.mega.platform.cloud.data.entity.Project;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 超级管理员权限管理服务类
 */
@Service
@Slf4j
public class AdminSuperAccessService {

    private final AdminSuperAccessDao adminSuperAccessDao;

    @Autowired
    public AdminSuperAccessService(AdminSuperAccessDao adminSuperAccessDao) {
        this.adminSuperAccessDao = adminSuperAccessDao;
    }

    /**
     * 查询传入的userId是否是超级管理员(role_id = 1)
     * @param userId 用户ID
     * @return 是否为超级管理员
     */
    public boolean isSuperAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        return adminSuperAccessDao.isSuperAdmin(userId);
    }

    public boolean isSuperAdminByRole(Long roleId){
        if(roleId != null && roleId.equals(1L)){
            return true;
        }
        return false;
    }

    /**
     * 查询超级管理员绑定的项目列表
     * 查询数据库所有的项目，然后与方法入参的user_id，封装为List<AdminUserProjectBinding>返回
     * @param userId 用户ID
     * @return 用户项目绑定列表
     */
    public List<AdminUserProjectBindingDTO> getSuperAdminProjectBindings(Long userId) {        
        // 查询所有项目
        List<Project> allProjects = adminSuperAccessDao.selectAllProjects();
        
        // 封装为AdminUserProjectBinding列表
        return allProjects.stream()
            .map(project -> {
                AdminUserProjectBindingDTO binding = new AdminUserProjectBindingDTO();
                binding.setAdminUserId(userId);
                binding.setProjectId(project.getId());
                binding.setProjectName(project.getName());
                binding.setDelsign(false);
                return binding;
            })
            .collect(Collectors.toList());
    }

    /**
     * 查询超级管理员绑定的router列表
     * 查询数据库所有路由，然后与方法入参的user_id，封装为List<AdminUserRouterBinding>返回
     * @param userId 用户ID
     * @return 用户路由绑定列表
     */
    public List<AdminUserRouterBinding> getSuperAdminRouterBindings(Long userId) {        
        // 查询所有路由
        List<AdminRouter> allRouters = adminSuperAccessDao.selectAllRouters();
        
        // 封装为AdminUserRouterBinding列表
        return allRouters.stream()
            .map(router -> {
                AdminUserRouterBinding binding = new AdminUserRouterBinding();
                binding.setAdminUserId(userId);
                binding.setAdminRouterId(router.getId());
                binding.setDelsign(false);
                return binding;
            })
            .collect(Collectors.toList());
    }


    /**
     * 查询超级管理员绑定的role列表
     * 查询数据库所有路由，然后与方法入参的user_id，封装为List<AdminRoleRouterBinding>返回
     * @return
     */
    public List<AdminRoleRouterBindingDTO> getSuperRoleRouterBindings(){
                // 查询所有路由
        List<AdminRouter> allRouters = adminSuperAccessDao.selectAllRouters();
        
        // 封装为AdminUserRouterBinding列表
        return allRouters.stream()
            .map(router -> {
                AdminRoleRouterBindingDTO binding = new AdminRoleRouterBindingDTO();
                binding.setAdminRoleId(1L);
                binding.setAdminRouterId(router.getId());
                binding.setDelsign(false);
                binding.setRouterName(router.getFrontendName());
                return binding;
            })
            .collect(Collectors.toList());
    }

    /**
     * 查询超级管理员绑定的role列表
     * 查询数据库所有项目，然后与方法入参的user_id，封装为List<AdminRoleProjectBinding>返回
     * @return
     */
    public List<AdminRoleProjectBindingDTO> getSuperRoleProjectBindings() {
        // 查询所有项目
        List<Project> allProjects = adminSuperAccessDao.selectAllProjects();
        
        // 封装为AdminUserProjectBinding列表
        return allProjects.stream()
            .map(project -> {
                AdminRoleProjectBindingDTO binding = new AdminRoleProjectBindingDTO();
                binding.setAdminRoleId(1L);
                binding.setProjectId(project.getId());
                binding.setProjectName(project.getName());
                binding.setDelsign(false);
                return binding;
            })
            .collect(Collectors.toList());
    }
}