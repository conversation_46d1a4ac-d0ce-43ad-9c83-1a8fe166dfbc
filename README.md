mega-cloud/
├── base/
│   ├── mega-cloud-client/        # feign接口客户端
│   ├── mega-cloud-data/          # 项目数据结构（entity、vo、dto等）
│   ├── mega-cloud-common/        # 公共基础工具模块（工具类、常量、通用封装等）
│   ├── mega-cloud-core/          # 核心基础模块（领域核心代码、基础能力）
│   └── mega-cloud-generator/     # 代码/数据生成器模块（如代码自动生成、脚本生成等）
├── management/
│   ├── mega-cloud-admin/         # 后台管理主模块（后台管理系统相关业务）
│   ├── mega-cloud-access/    # 后台管理认证鉴权模块（后台权限、登录、认证、授权等）
│   └── mega-cloud-monitor/       # 服务监控模块（健康检查、服务运行监控、日志采集等）
├── microservice/
│   └── mega-cloud-microservice/ # 微服务主模块（聚合/协调微服务能力）
├── third-party/
│   ├── mega-cloud-auth/          # 第三方认证对接模块（如第三方平台登录/认证）
│   ├── mega-cloud-payment/       # 第三方支付/充值对接模块（如微信、支付宝支付能力等）
│   └── mega-cloud-push/          # 第三方推送对接模块（如短信、邮件、推送等）
├── .gitignore                    # Git 忽略规则配置文件
├── mega-cloud.sh                 # 项目一键启动/管理脚本
├── pom.xml                       # Maven 主项目配置文件
├── README.md                     # 项目说明文档




## mega-cloud-admin路由列表

路由规则：
* /admin/api/public/{controller}/xxx 表示无需鉴权，比如登陆接口  
* /admin/api/system/{controller}/xxx 表示系统级别的权限，比如创建项目等，只校验token有效性
* /admin/api/{project_id}/{controller}/xxx  表示项目级别权限，除了校验token有效性，还需要校验项目权限，

### 登陆/登出
* POST /admin/api/public/auth/login  - 登陆
* POST /admin/api/system/auth/logout - 登出
* POST /admin/api/system/auth/logout-all - 登出全部

### 项目管理模块
* POST /admin/api/system/project/list - 项目列表查询
* POST /admin/api/system/project/create - 创建项目
* POST /admin/api/{project_id}/project/detail - 项目详情
* POST /admin/api/{project_id}/project/edit - 项目编辑
* POST /admin/api/{project_id}/project/delete - 项目删除

### 项目应用管理模块
* POST /admin/api/{project_id}/app/list - 项目应用列表
* POST /admin/api/{project_id}/app/create - 创建项目应用
* POST /admin/api/{project_id}/app/detail - 项目应用详情
* POST /admin/api/{project_id}/app/edit - 项目应用编辑
* POST /admin/api/{project_id}/app/delete - 项目应用删除

## 应用验证

### 验证路由规则
- **公共接口不验证 token**：`/{$module-name}/api/**/public/**`

## 接口获取 appId 的两种方式

### 1. 带 @RequestBody 的接口

- 接口通过 `@RequestBody` 解析请求体，从 VO 中获取 `appId`。

#### 示例代码：

```java
@ApiOperation("测试token")
@PostMapping("/parse/token")
public Result<?> parseToken(@RequestBody BaseReqVO vo) {
    log.info(vo.getAppId().toString());
    return Results.success(vo.getAppId());
}
```

### 2. 不带 @RequestBody 的接口
- 通过 HttpServletRequest 获取中台解析并注入的 appId，适用于不需要body的接口。
```java
@ApiOperation("测试token")
@PostMapping("/parse/token")
public Result<?> parseToken(HttpServletRequest request) {
    log.info(String.valueOf(request.getAttribute(JwtConstants.CLAIM_APP_ID)));
    return Results.success();
}
```



