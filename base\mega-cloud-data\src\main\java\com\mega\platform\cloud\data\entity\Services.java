package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "services")
public class Services {
    /**
     * 微服务id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 微服务名
     */
    @Column(name = "name")
    private String name;

    /**
     * 微服务组id
     */
    @Column(name = "services_group_id")
    private Long servicesGroupId;

    /**
     * 服务器id
     */
    @Column(name = "ecs_server_id")
    private Long ecsServerId;

    /**
     * jenkins_job表id
     */
    @Column(name = "jenkins_job_id")
    private Long jenkinsJobId;

    /**
     * 程序路径
     */
    @Column(name = "path")
    private String path;

    /**
     * 日志路径
     */
    @Column(name = "log_path")
    private String logPath;

    /**
     * 产品版本
     */
    @Column(name = "version")
    private String version;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 日志超时时间
     */
    @Column(name = "log_timeout_second")
    private Integer logTimeoutSecond;

    /**
     * 状态 2001
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 日志状态
     */
    @Column(name = "log_status")
    private Integer logStatus;

    /**
     * 运行状态 2002
     */
    @Column(name = "running_status")
    private Integer runningStatus;

    /**
     * 真实运行状态 2002
     */
    @Column(name = "real_running_status")
    private Integer realRunningStatus;

    /**
     * 升序
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}