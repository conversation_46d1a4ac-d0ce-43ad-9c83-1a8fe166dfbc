package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
@Data
@Accessors(chain = true)
@ApiModel("应用商品配置更新请求参数")
public class AdminAppProductConfigUpdateReqVO {
    @ApiModelProperty("productId")
    private Long productId;

    @ApiModelProperty("关联的支付平台ID")
    private Long thirdPlatformId;

    @ApiModelProperty("外部商品ID（比如苹果平台的sku）")
    private String externalProductId;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("是否为订阅产品")
    private Boolean isSubscription;

    @ApiModelProperty("订阅周期（天）")
    private Integer subscriptionPeriodDay;

    @ApiModelProperty("商品价格")
    private BigDecimal amount;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("备注")
    private String remark;
}
