package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_task_log")
public class JenkinsTaskLog {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 关联jenkins_task_group(id)
     */
    @Column(name = "jenkins_task_group_id")
    private Long jenkinsTaskGroupId;

    /**
     * 关联jenkins_task(id)
     */
    @Column(name = "jenkins_task_id")
    private Long jenkinsTaskId;

    /**
     * 日志生成时间
     */
    @Column(name = "log_time")
    private Date logTime;

    /**
     * 日志内容
     */
    @Column(name = "log_content")
    private String logContent;
}