package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
@Data
public class AuthWeChatUserInfoReqVO extends BaseReqVO {
    @NotBlank(message = "code不能为空")
    private String code;

    @NotBlank(message = "clientIp不能为空")
    private String clientIp;

    @NotBlank(message = "deviceId不能为空")
    private String deviceId;
}
