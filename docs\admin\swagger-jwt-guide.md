# Swagger JWT Token 认证使用指南

## 概述

本文档介绍如何在 Mega Cloud Admin 模块中使用 Swagger UI 进行 JWT Token 认证的 API 测试。

## 配置说明

### 1. 依赖配置

在 `pom.xml` 中已添加 Swagger 依赖：

```xml
<!-- Springfox Swagger（API文档）-->
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-boot-starter</artifactId>
</dependency>
```

### 2. Swagger 配置类

`SwaggerConfig.java` 配置了：
- API 文档基本信息
- JWT Bearer Token 认证支持
- 安全上下文配置

### 3. 应用配置

在 `application.yml` 中配置了 Swagger 相关参数。

## 使用方法

### 1. 访问 Swagger UI

启动应用后，访问以下地址：

```
http://localhost:8081/swagger-ui/index.html
```

### 2. JWT Token 认证

#### 步骤 1：获取 JWT Token

首先需要通过登录接口获取 JWT Token。

#### 步骤 2：配置 adminToken

1. 在 Swagger UI 页面右上角点击 **"Authorize"** 按钮
2. 在弹出的对话框中，找到 **"adminToken (apiKey)"** 输入框
3. 直接输入 JWT Token（无需任何前缀）
   
   例如：
   ```
   eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```
   
   **注意**：直接输入纯 JWT Token 值，系统会自动添加到 adminToken 请求头中

4. 点击 **"Authorize"** 按钮确认
5. 点击 **"Close"** 关闭对话框

#### 步骤 3：测试 API

配置完成后，所有标记为需要认证的 API 都会自动在请求头中包含正确格式的 adminToken 信息：

```
adminToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. 测试接口

提供了以下测试接口：

- **GET /admin/swagger-test/public** - 公开接口，无需认证
- **GET /admin/swagger-test/protected** - 受保护接口，需要 JWT Token
- **GET /admin/swagger-test/user-info** - 获取用户信息，需要 JWT Token

## 技术实现

### adminToken 配置

系统已配置为使用自定义的 `adminToken` 头字段：

```java
private List<SecurityScheme> securitySchemes() {
    return Collections.singletonList(
            ApiKey.apiKey("adminToken", "adminToken", In.HEADER)
                    .description("JWT Token认证")
    );
}
```

这种配置方式确保：
- 用户直接输入 JWT Token 值
- Swagger 在请求头中添加 "adminToken" 字段
- 避免与其他 Bearer token 处理器产生冲突
- 请求头格式：`adminToken: <token>`

## 注意事项

1. **Token 格式**：只需输入纯 JWT Token，系统会自动添加到 adminToken 请求头中
2. **Token 有效期**：请确保使用的 JWT Token 在有效期内
3. **权限验证**：某些接口可能需要特定的权限，请确保当前用户具有相应权限
4. **HTTPS**：生产环境建议使用 HTTPS 协议保护 Token 安全

## 常见问题

### Q1: 为什么我的请求返回 401 Unauthorized？
**A**: 可能的原因：
- JWT Token 已过期
- JWT Token 格式不正确
- 用户权限不足
- adminToken 头字段未正确设置

### Q2: 如何查看实际发送的请求头？
**A**: 在浏览器开发者工具的 Network 标签页中查看请求详情，确认 adminToken 头格式是否正确。

### Q3: Token 输入后没有生效？
**A**: 请检查：
- 是否点击了 "Authorize" 按钮确认
- Token 是否完整复制（注意不要包含多余的空格或换行）
- 是否在正确的输入框中输入（应该是 adminToken 输入框）

### Q4: 403 Forbidden 错误

**原因**：用户权限不足

**解决方法**：
- 确认当前用户是否具有访问该接口的权限
- 联系管理员分配相应权限

### Q5: Swagger UI 无法访问

**原因**：应用未正确启动或端口配置错误

**解决方法**：
- 确认应用已正确启动
- 检查端口配置（默认 8081）
- 查看应用日志排查启动问题

### Q6: Authorization 按钮显示异常

**原因**：Swagger 配置问题

**解决方法**：
- 确认 SwaggerConfig 配置正确
- 重启应用
- 清除浏览器缓存

## 开发建议

1. **API 文档**：为所有接口添加详细的 Swagger 注解
2. **权限标记**：明确标记哪些接口需要认证
3. **错误处理**：提供清晰的错误信息和状态码
4. **版本管理**：合理规划 API 版本
5. **安全考虑**：生产环境使用 HTTPS，定期更新 JWT 密钥

## 相关文件

- `SwaggerConfig.java` - Swagger 配置类
- `SwaggerTestController.java` - 测试控制器
- `application.yml` - 应用配置文件
- `pom.xml` - Maven 依赖配置