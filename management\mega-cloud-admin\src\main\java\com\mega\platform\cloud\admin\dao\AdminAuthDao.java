package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.data.entity.AdminRole;
import com.mega.platform.cloud.data.entity.AdminRouter;
import com.mega.platform.cloud.data.entity.AdminUser;
import com.mega.platform.cloud.data.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管理员认证数据访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface AdminAuthDao {

    /**
     * 根据用户名查询管理员用户
     *
     * @param username 用户名
     * @return 管理员用户信息
     */
    AdminUser findByUsername(@Param("username") String username);

    /**
     * 根据用户ID查询管理员用户
     *
     * @param adminUserId 管理员用户ID
     * @return 管理员用户信息
     */
    AdminUser findById(@Param("adminUserId") Long adminUserId);

    /**
     * 根据管理员用户ID查询用户角色列表
     *
     * @param adminUserId 管理员用户ID
     * @return 角色列表
     */
    List<AdminRole> findRolesByAdminUserId(@Param("adminUserId") Long adminUserId);

    /**
     * 根据角色ID列表查询路由权限列表
     *
     * @param roleIds 角色ID列表
     * @return 路由权限列表
     */
    List<AdminRouter> findRoutersByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据管理员用户ID查询直接分配的路由权限列表
     *
     * @param adminUserId 管理员用户ID
     * @return 路由权限列表
     */
    List<AdminRouter> findRoutersByAdminUserId(@Param("adminUserId") Long adminUserId);

    /**
     * 根据角色ID列表查询关联的项目列表
     *
     * @param roleIds 角色ID列表
     * @return 项目列表
     */
    List<Project> findProjectsByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据管理员用户ID查询直接分配的项目列表
     *
     * @param adminUserId 管理员用户ID
     * @return 项目列表
     */
    List<Project> findProjectsByAdminUserId(@Param("adminUserId") Long adminUserId);

    /**
     * 更新管理员用户最后登录时间
     *
     * @param adminUserId 管理员用户ID
     */
    void updateLastLoginTime(@Param("adminUserId") Long adminUserId);

}