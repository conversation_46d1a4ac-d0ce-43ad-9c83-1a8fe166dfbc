package com.mega.platform.cloud.common.utils;

import java.util.Random;

import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_0;
import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_1;

public class StringUtils extends org.apache.commons.lang3.StringUtils {

    /**
     * 将小时数转换为可读的时间字符串
     * 规则：
     * - 超过24小时显示天数
     * - 24小时内显示小时数
     * - 小于1小时显示分钟数
     *
     * @param hours 小时数
     * @return 格式化后的时间字符串
     */
    public static String formatHoursToTimeString(float hours) {
        StringBuilder result = new StringBuilder();

        // Calculate days
        if (hours >= 24) {
            int days = (int) (hours / 24);
            result.append(days).append("天");
            hours = hours % 24;
        }

        // Calculate remaining hours
        if (hours >= 1) {
            int remainingHours = (int) hours;
            result.append(remainingHours).append("小时");
            hours = hours - remainingHours;
        }

        // Calculate remaining minutes
        int minutes = (int) (hours * 60);
        if (minutes > 0) {
            result.append(minutes).append("分钟");
        }

        return result.toString();
    }

    public static String randomIntStr() {
        Random random = new Random();
        int randomNumber = random.nextInt(10000); // 生成 0-9999 之间的随机数
        return String.format("%04d", randomNumber); // 格式化为 4 位数字，前导 0 保留
    }

    public static Byte booleanToByte(Boolean bool) {
        if (bool == null || !bool) {
            return BYTE_0;
        }
        return BYTE_1;
    }
}
