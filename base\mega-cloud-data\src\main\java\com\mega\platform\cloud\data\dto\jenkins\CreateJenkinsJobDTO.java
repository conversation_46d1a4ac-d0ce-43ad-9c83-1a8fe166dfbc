package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class CreateJenkinsJobDTO {
    private String jobName;
    private String xml;
    private Map<String, String> paramMap;

    public CreateJenkinsJobDTO(String jobName, String xml, Map<String, String> paramMap) {
        this.jobName = jobName;
        this.xml = xml;
        this.paramMap = paramMap;
    }
}
