package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-角色列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色列表响应")
public class AdminAccessRoleListRespVO {

    @ApiModelProperty("角色ID")
    private Long adminRoleId;

    @ApiModelProperty("角色名称")
    private String adminRoleName;

    @ApiModelProperty("角色描述")
    private String adminRoleDescription;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}