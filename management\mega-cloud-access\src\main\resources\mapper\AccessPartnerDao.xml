<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.access.dao.AccessPartnerDao">

    <!-- 根据appKey查询项目应用信息 -->
    <select id="selectProjectAppByappKey" resultType="com.mega.platform.cloud.data.entity.ProjectApp">
        SELECT id, app_key, app_secret, name, project_id, status, remark, create_time, update_time, delsign
        FROM project_app
        WHERE app_key = #{appKey}
          AND delsign = 0
          AND status = 1
    </select>

    <!-- 根据项目应用ID和服务名称查询加密信息 -->
    <select id="selectCryptoByProjectAppIdAndServerName" resultType="com.mega.platform.cloud.data.entity.AccessPartnerCrypto">
        SELECT project_app_id, server_name, crypto_key, md5_json, create_time, update_time, delsign
        FROM access_partner_crypto
        WHERE project_app_id = #{projectAppId}
          AND server_name = #{serverName}
          AND delsign = 0
    </select>

    <!-- 根据项目应用ID、机器ID和服务名称查询License激活状态 -->
    <select id="selectLicenseActiveByProjectAppIdAndMachineIdAndServerName" resultType="com.mega.platform.cloud.data.entity.AccessPartnerLicenseActive">
        SELECT project_app_id, machine_id, server_name, license, state, create_time, update_time, delsign
        FROM access_partner_license_active
        WHERE project_app_id = #{projectAppId}
          AND machine_id = #{machineId}
          AND server_name = #{serverName}
          AND delsign = 0
    </select>

    <!-- 插入License激活状态记录 -->
    <insert id="insertLicenseActive">
        INSERT INTO access_partner_license_active (
            project_app_id, machine_id, server_name, license, state, create_time, update_time, delsign
        ) VALUES (
            #{projectAppId}, #{machineId}, #{serverName}, #{license}, #{state}, NOW(), NOW(), 0
        )
    </insert>

    <!-- 更新License激活状态 -->
    <update id="updateLicenseActive">
        UPDATE access_partner_license_active
        SET license = #{license},
            state = #{state},
            update_time = NOW()
        WHERE project_app_id = #{projectAppId}
          AND machine_id = #{machineId}
          AND server_name = #{serverName}
          AND delsign = 0
    </update>

    <!-- 插入License激活记录 -->
    <insert id="insertLicenseActiveRecord" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO access_partner_license_active_record (
            project_app_id, inner_ip, public_ip, machine_id, license, success, fail_reason, server_name, create_time, update_time
        ) VALUES (
            #{projectAppId}, #{innerIp}, #{publicIp}, #{machineId}, #{license}, #{success}, #{failReason}, #{serverName}, NOW(), NOW()
        )
    </insert>

</mapper>
