<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminAccessDao">

    <!-- ==================== 用户管理 ==================== -->
    <!-- 查询用户列表 -->
    <select id="selectUserList" resultType="com.mega.platform.cloud.data.entity.AdminUser">
        SELECT id, username, password, last_login_time, create_time, update_time, delsign
        FROM admin_user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="delsign != null">
                AND delsign = #{delsign}
            </if>
        </where>
        ORDER BY create_time DESC

    </select>
    
    <!-- 插入用户 -->
    <insert id="insertUser" parameterType="com.mega.platform.cloud.data.entity.AdminUser">
        INSERT INTO admin_user (username, password, create_time, update_time, delsign)
        VALUES (#{username}, #{password}, NOW(), NOW(), 0)
    </insert>
    
    <!-- 删除用户（逻辑删除） -->
    <update id="deleteUser">
        UPDATE admin_user 
        SET delsign = 1
        WHERE id = #{userId}
    </update>
    
    <!-- 更新用户密码 -->
    <update id="updateUserPassword">
        UPDATE admin_user 
        SET password = #{password}, update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- ==================== 用户项目绑定管理 ==================== -->
    <!-- 用户项目绑定查询条件 -->
    <sql id="userProjectBindingWhere">
        <where>
            <if test="userId != null">
                AND aupb.admin_user_id = #{userId}
            </if>
            <if test="delsign != null">
                AND aupb.delsign = #{delsign}
            </if>
            AND aupb.delsign = 0
        </where>
    </sql>
    
    <!-- 查询用户项目绑定列表 -->
    <select id="selectUserProjectBindingList" resultType="com.mega.platform.cloud.admin.dto.AdminUserProjectBindingDTO">
        SELECT 
            aupb.admin_user_id,
            aupb.project_id,
            ap.name as project_name,
            aupb.delsign
        FROM admin_user_project_binding aupb
        LEFT JOIN admin_user au ON aupb.admin_user_id = au.id
        LEFT JOIN project ap ON aupb.project_id = ap.id
        <include refid="userProjectBindingWhere"/>
    </select>
        
    <!-- 批量插入用户项目绑定 -->
    <insert id="batchInsertUserProjectBinding">
        INSERT INTO admin_user_project_binding (admin_user_id, project_id, create_time, delsign)
        VALUES
        <foreach collection="bindings" item="binding" separator=",">
            (#{binding.adminUserId}, #{binding.projectId}, NOW(), 0)
        </foreach>
    </insert>
    
    <!-- 插入或更新用户项目绑定（冲突时更新delsign） -->
    <insert id="insertOrUpdateUserProjectBinding" parameterType="com.mega.platform.cloud.data.entity.AdminUserProjectBinding">
        INSERT INTO admin_user_project_binding (admin_user_id, project_id, create_time, delsign)
        VALUES (#{adminUserId}, #{projectId}, NOW(), #{delsign})
        ON DUPLICATE KEY UPDATE 
            delsign = VALUES(delsign)
    </insert>

    <!-- ==================== 用户路由绑定管理 ==================== -->
    
    <!-- 查询用户路由绑定列表 -->
    <select id="selectUserRouteBindingList" resultType="com.mega.platform.cloud.data.entity.AdminUserRouterBinding">
        SELECT aurb.id, aurb.admin_user_id, aurb.admin_router_id, aurb.create_time, aurb.delsign
        FROM admin_user_router_binding aurb
        LEFT JOIN admin_user au ON aurb.admin_user_id = au.id
        LEFT JOIN admin_router ar ON aurb.admin_router_id = ar.id
        <where>
            <if test="userId != null">
                AND aurb.admin_user_id = #{userId}
            </if>
            <if test="routerId != null">
                AND aurb.admin_router_id = #{routerId}
            </if>
            <if test="delsign != null">
                AND aurb.delsign = #{delsign}
            </if>
            AND aurb.delsign = 0
        </where>
        ORDER BY aurb.create_time DESC
    </select>
    
    <!-- 删除用户路由绑定 -->
    <update id="deleteUserRouteBinding">
        UPDATE admin_user_router_binding 
        SET delsign = 1
        WHERE admin_user_id = #{userId}
    </update>
    
    <!-- 批量插入用户路由绑定 -->
    <insert id="batchInsertUserRouteBinding">
        INSERT INTO admin_user_router_binding (admin_user_id, admin_router_id, create_time, delsign)
        VALUES
        <foreach collection="bindings" item="binding" separator=",">
            (#{binding.adminUserId}, #{binding.adminRouterId}, NOW(), 0)
        </foreach>
    </insert>
    
    <!-- 插入或更新用户路由绑定（冲突时更新delsign） -->
    <insert id="insertOrUpdateUserRouterBinding" parameterType="com.mega.platform.cloud.data.entity.AdminUserRouterBinding">
        INSERT INTO admin_user_router_binding (admin_user_id, admin_router_id, create_time, delsign)
        VALUES (#{adminUserId}, #{adminRouterId}, NOW(), #{delsign})
        ON DUPLICATE KEY UPDATE
            delsign = VALUES(delsign)
    </insert>

    <!-- ==================== 角色管理 ==================== -->
    
    <!-- 查询角色列表 -->
    <select id="selectRoleList" resultType="com.mega.platform.cloud.data.entity.AdminRole">
        SELECT id, name, description, create_time, update_time, delsign
        FROM admin_role
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="delsign != null">
                AND delsign = #{delsign}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 插入角色 -->
    <insert id="insertRole" parameterType="com.mega.platform.cloud.data.entity.AdminRole">
        INSERT INTO admin_role (name, description, create_time, update_time, delsign)
        VALUES (#{name}, #{description}, NOW(), NOW(), 0)
    </insert>
    
    <!-- 删除角色（逻辑删除） -->
    <update id="deleteRole">
        UPDATE admin_role 
        SET delsign = 1, update_time = NOW()
        WHERE id = #{roleId}
    </update>

    <!-- ==================== 角色路由绑定管理 ==================== -->
    
    <!-- 查询角色路由绑定列表 -->
    <select id="selectRoleRouteBindingList" resultType="com.mega.platform.cloud.admin.dto.AdminRoleRouterBindingDTO">
        SELECT arrb.id, 
        arrb.admin_role_id, 
        arrb.admin_router_id, 
        aro.frontend_name as router_name,  
        arrb.delsign
        FROM admin_role_router_binding arrb
        LEFT JOIN admin_role ar ON arrb.admin_role_id = ar.id
        LEFT JOIN admin_router aro ON arrb.admin_router_id = aro.id
        <where>
            AND aro.parent_admin_router_id = 0
            <if test="roleId != null">
                AND arrb.admin_role_id = #{roleId}
            </if>
            <if test="routerId != null">
                AND arrb.admin_router_id = #{routerId}
            </if>
            <if test="delsign != null">
                AND arrb.delsign = #{delsign}
            </if>
            AND arrb.delsign = 0
        </where>
        ORDER BY arrb.create_time DESC
    </select>
    
    <!-- 删除角色路由绑定 -->
    <update id="deleteRoleRouteBinding">
        UPDATE admin_role_router_binding 
        SET delsign = 1
        WHERE admin_role_id = #{roleId}
    </update>
    
    <!-- 批量插入角色路由绑定 -->
    <insert id="batchInsertRoleRouteBinding">
        INSERT INTO admin_role_router_binding (admin_role_id, admin_router_id, create_time, delsign)
        VALUES
        <foreach collection="bindings" item="binding" separator=",">
            (#{binding.adminRoleId}, #{binding.adminRouterId}, NOW(), 0)
        </foreach>
    </insert>
    
    <!-- 插入或更新角色路由绑定（冲突时更新delsign） -->
    <insert id="insertOrUpdateRoleRouterBinding" parameterType="com.mega.platform.cloud.data.entity.AdminRoleRouterBinding">
        INSERT INTO admin_role_router_binding (admin_role_id, admin_router_id, create_time, delsign)
        VALUES (#{adminRoleId}, #{adminRouterId}, NOW(), #{delsign})
        ON DUPLICATE KEY UPDATE
            delsign = VALUES(delsign)
    </insert>

    <!-- ==================== 角色项目绑定管理 ==================== -->
    
    <!-- 查询角色项目绑定列表 -->
    <select id="selectRoleProjectBindingList" resultType="com.mega.platform.cloud.admin.dto.AdminRoleProjectBindingDTO">
        SELECT arpb.id, 
        arpb.admin_role_id, 
        arpb.project_id, 
        arpb.create_time, 
        ap.name as project_name,
        arpb.delsign
        FROM admin_role_project_binding arpb
        LEFT JOIN admin_role ar ON arpb.admin_role_id = ar.id
        LEFT JOIN project ap ON arpb.project_id = ap.id
        <where>
            <if test="roleId != null">
                AND arpb.admin_role_id = #{roleId}
            </if>
            <if test="projectId != null">
                AND arpb.project_id = #{projectId}
            </if>
            <if test="delsign != null">
                AND arpb.delsign = #{delsign}
            </if>
            AND arpb.delsign = 0
        </where>
        ORDER BY arpb.create_time DESC
    </select>
    
    <!-- 删除角色项目绑定 -->
    <update id="deleteRoleProjectBinding">
        UPDATE admin_role_project_binding 
        SET delsign = 1
        WHERE admin_role_id = #{roleId}
    </update>
    
    <insert id="insertOrUpdateRoleProjectBinding">
        INSERT INTO admin_role_project_binding (admin_role_id, project_id, create_time, delsign)
        VALUES (#{adminRoleId}, #{projectId}, NOW(), #{delsign})
        ON DUPLICATE KEY UPDATE
            delsign = VALUES(delsign)
    </insert>

    <!-- ==================== 角色用户绑定管理 ==================== -->
    
    <!-- 查询角色用户绑定列表 -->
    <select id="selectRoleUserBindingList" resultType="com.mega.platform.cloud.admin.dto.AdminUserRoleBindingDTO">
        SELECT 
        aurb.admin_role_id, 
        aurb.admin_user_id, 
        au.username as admin_user_name,
        
        aurb.delsign
        FROM admin_user_role_binding aurb
        LEFT JOIN admin_role ar ON aurb.admin_role_id = ar.id
        LEFT JOIN admin_user au ON aurb.admin_user_id = au.id
        <where>
            <if test="roleId != null">
                AND aurb.admin_role_id = #{roleId}
            </if>
            <if test="userId != null">
                AND aurb.admin_user_id = #{userId}
            </if>
            <if test="delsign != null">
                AND aurb.delsign = #{delsign}
            </if>
            AND aurb.delsign = 0
        </where>
        ORDER BY aurb.create_time DESC
    </select>
    
    <!-- 删除角色用户绑定 -->
    <update id="deleteRoleUserBinding">
        UPDATE admin_user_role_binding 
        SET delsign = 1
        WHERE admin_role_id = #{roleId}
    </update>
    
    <insert id="insertOrUpdateRoleUserBinding">
     INSERT INTO admin_user_role_binding (admin_role_id, admin_user_id, create_time, delsign)
        VALUES (#{adminRoleId}, #{adminUserId}, NOW(), #{delsign})
        ON DUPLICATE KEY UPDATE
            delsign = VALUES(delsign)
    </insert>

    <!-- ==================== 路由管理 ==================== -->
    
    <!-- 查询路由列表 -->
    <select id="selectRouterList" resultType="com.mega.platform.cloud.data.entity.AdminRouter">
        SELECT id, backend_path, frontend_path, frontend_name, description, parent_admin_router_id, create_time, update_time, delsign
        FROM admin_router
        <where>
            <if test="backendPath != null and backendPath != ''">
                AND backend_path LIKE CONCAT('%', #{backendPath}, '%')
            </if>
            <if test="frontendPath != null and frontendPath != ''">
                AND frontend_path LIKE CONCAT('%', #{frontendPath}, '%')
            </if>
            <if test="frontendName != null and frontendName != ''">
                AND frontend_name LIKE CONCAT('%', #{frontendName}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="parentAdminRouterId != null">
                AND parent_admin_router_id = #{parentAdminRouterId}
            </if>
            <if test="delsign != null">
                AND delsign = #{delsign}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>


    <!-- selectRouterById --> 

    <select id="selectRouterById" resultType="com.mega.platform.cloud.data.entity.AdminRouter">
        SELECT id, backend_path, frontend_path, frontend_name, description, parent_admin_router_id, create_time, update_time, delsign
        FROM admin_router
        WHERE id = #{routerId}
    </select>
</mapper>