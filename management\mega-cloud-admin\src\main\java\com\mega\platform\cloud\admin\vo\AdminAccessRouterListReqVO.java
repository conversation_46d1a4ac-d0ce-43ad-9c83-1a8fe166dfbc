package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-路由列表请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-路由列表请求参数")
public class AdminAccessRouterListReqVO {

    // @ApiModelProperty(value = "页码", example = "1")
    // private Integer pageNum = 1;

    // @ApiModelProperty(value = "每页大小", example = "10")
    // private Integer pageSize = 10;

    // @ApiModelProperty(value = "后端路由路径", example = "/admin/api")
    private String backendPath;

    // @ApiModelProperty(value = "前端路由路径", example = "/dashboard")
    private String frontendPath;

    // @ApiModelProperty(value = "前端路由名称", example = "Dashboard")
    private String frontendName;

    // @ApiModelProperty(value = "路由描述", example = "仪表板")
    private String description;

    // @ApiModelProperty(value = "父路由ID", example = "1")
    private Long parentAdminRouterId;

    // @ApiModelProperty(value = "删除标识", example = "0")
    private Integer delsign = 0;
}