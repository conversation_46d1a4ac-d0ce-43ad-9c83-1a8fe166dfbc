# Swagger Bearer Token 认证使用指南

## 问题描述

在 mega-cloud-access 模块的 Swagger UI 中，原本显示的是 "Authorization (apiKey)" 而不是标准的 Bearer Token 格式，导致用户需要手动添加 "Bearer " 前缀。

## 问题原因

### 1. 原始配置问题
原始的 Swagger 配置使用的是简单的 API Key 认证：
```java
private ApiKey bearerToken() {
    return new ApiKey("Authorization", "Authorization", "header");
}
```

### 2. 实际认证机制
系统实际使用的是 JWT Bearer Token 认证，期望的请求头格式为：
```
Authorization: Bearer <jwt_token>
```

JWT 认证过滤器会检查：
- 请求头名称：`Authorization`
- Token 前缀：`Bearer `
- 然后提取实际的 JWT token 进行验证

## 解决方案

### 1. 修改 Swagger 配置
已修改 `DocketConfig.java` 文件，使用 `HttpAuthenticationScheme` 替代 `ApiKey`：

```java
@Bean
public Docket boxApi() {
    return new Docket(DocumentationType.OAS_30)
            // ... 其他配置
            .securitySchemes(Collections.singletonList(bearerToken()))
            .securityContexts(Collections.singletonList(securityContext()));
}

private HttpAuthenticationScheme bearerToken() {
    return HttpAuthenticationScheme.JWT_BEARER_BUILDER
            .name("Authorization")
            .build();
}

private SecurityContext securityContext() {
    return SecurityContext.builder()
            .securityReferences(Collections.singletonList(
                    SecurityReference.builder()
                            .reference("Authorization")
                            .scopes(new AuthorizationScope[0])
                            .build()))
            .build();
}
```

### 2. 使用方法

#### 步骤 1：获取 JWT Token
首先调用登录接口获取 JWT Token：
```bash
curl -X POST "http://*************:8080/access/api/app/public/token" \
  -H "accept: */*" \
  -H "Content-Type: application/json" \
  -d "{\"appKey\":\"app_1752202145\",\"appSecret\":\"20b14c39e9094dd3aa262cc6a0f03789\"}"
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJhcHBJZCI6MSwiZXhwIjoxNzM..."
  }
}
```

#### 步骤 2：在 Swagger UI 中配置认证
1. 打开 Swagger UI：`http://localhost:8080/swagger-ui/index.html`
2. 点击页面右上角的 "Authorize" 按钮
3. 在弹出的 "Authorization (Bearer)" 对话框中，在 "Value" 字段中直接输入 JWT token
   - **注意**：现在只需要输入纯 JWT token，不需要 "Bearer " 前缀
   - 例如：`eyJhbGciOiJIUzI1NiJ9.eyJhcHBJZCI6MSwiZXhwIjoxNzM...`
4. 点击 "Authorize" 按钮
5. 现在所有需要认证的接口都会自动包含正确的 Authorization 头，格式为：`Authorization: Bearer <your_token>`

#### 步骤 3：测试认证接口
配置认证后，调用需要认证的接口时，Swagger 会自动添加正确的请求头：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9.eyJhcHBJZCI6MSwiZXhwIjoxNzM...
```

## 接口分类

### 公开接口（无需认证）
- `/access/api/app/public/token` - 获取登录 token
- `/access/api/partner/verify` - 获取 AES 密钥
- 其他 `/public/` 路径下的接口

### 需要认证的接口
- 所有非 `/public/` 路径下的接口都需要 Bearer Token 认证

## 修改效果

### 修改前
- 显示：`Authorization (apiKey)`
- 需要手动输入：`Bearer your_jwt_token_here`

### 修改后
- 显示：`Authorization (Bearer)`
- 只需输入：`your_jwt_token_here`
- Swagger 会自动添加 "Bearer " 前缀

## 注意事项

1. **Token 格式**：现在只需要输入纯 JWT token，不需要 "Bearer " 前缀
2. **Token 有效期**：JWT token 有过期时间（默认 3600 秒），过期后需要重新获取
3. **安全性**：不要在生产环境中暴露或分享 JWT Token

## 常见问题

### Q: 为什么我的请求返回 401 Unauthorized？
A: 请检查：
- JWT Token 是否正确
- Token 是否已过期
- 确保在 Swagger 中正确配置了 Authorization

### Q: 如何获取 JWT Token？
A: 通过调用登录接口（如 `/access/api/app/public/token`）获取 JWT Token。

### Q: Token 过期了怎么办？
A: 需要重新调用登录接口获取新的 JWT Token。

## 验证修复效果

修复后，在 Swagger UI 中：
1. "Available authorizations" 部分显示 "Authorization (Bearer)" 而不是 "Authorization (apiKey)"
2. 用户只需要输入纯 JWT token，不需要手动添加 "Bearer " 前缀
3. 配置认证后，请求会包含正确的 `Authorization: Bearer <token>` 头
4. 需要认证的接口应该能够正常访问

## 相关文件

- `base/mega-cloud-common/src/main/java/com/mega/platform/cloud/common/config/DocketConfig.java` - Swagger 配置
- `base/mega-cloud-common/src/main/java/com/mega/platform/cloud/common/constant/JwtConstants.java` - JWT 常量定义
- `base/mega-cloud-common/src/main/java/com/mega/platform/cloud/common/filter/JwtAuthenticationFilter.java` - JWT 认证过滤器
