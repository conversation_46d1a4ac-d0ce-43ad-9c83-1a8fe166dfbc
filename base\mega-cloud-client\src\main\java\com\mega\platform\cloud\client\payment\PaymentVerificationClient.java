package com.mega.platform.cloud.client.payment;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.auth.AuthSendSmsCodeReqVO;
import com.mega.platform.cloud.data.vo.auth.AuthSendSmsCodeRespVO;
import com.mega.platform.cloud.data.vo.payment.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;

@FeignClient(value = "mega-cloud-payment-" + "${spring.profiles.active}", contextId = "mega-cloud-payment-verification-client")
@RequestMapping("/payment/api/verification")
@Api(tags = "支付校验接口")
public interface PaymentVerificationClient {
    @ApiOperation("苹果交易校验")
    @PostMapping("/apple/transaction")
    public Result<PaymentAppleVerifyTransactionRespVO> verifyAppleTransaction(@Validated @RequestBody PaymentAppleVerifyTransactionReqVO vo) throws IOException;

    @ApiOperation("苹果交易回调")
    @PostMapping("/apple/transaction/callback")
    public Result<PaymentAppleCallbackRespVO> appleCallback(@Validated @RequestBody PaymentAppleCallbackReqVO vo);

    @ApiOperation("支付宝APP交易创建")
    @PostMapping("/alipay/app/transaction/create")
    public Result<PaymentAlipayCreateRespVO> createAlipayTransaction(@Validated @RequestBody PaymentAlipayCreateReqVO vo);

    @ApiOperation("支付宝交易回调")
    @PostMapping("/alipay/transaction/callback")
    public Result<PaymentAlipayCallbackRespVO> alipayCallback(@Validated @RequestBody PaymentAlipayCallbackReqVO vo);

    @ApiOperation("微信APP交易创建")
    @PostMapping("/wechat/app/transaction/create")
    public Result<PaymentWeChatCreateRespVO>  createWeChatTransaction(@Validated @RequestBody PaymentWeChatCreateReqVO vo);

    @ApiOperation("微信交易回调")
    @PostMapping("/wechat/transaction/callback")
    public Result<PaymentWeChatCallbackRespVO>  wechatCallback(@Validated @RequestBody PaymentWeChatCallbackReqVO vo);
}
