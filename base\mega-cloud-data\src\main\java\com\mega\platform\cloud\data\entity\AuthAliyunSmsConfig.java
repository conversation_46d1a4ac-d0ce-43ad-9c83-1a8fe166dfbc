package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("阿里云短信配置")
public class AuthAliyunSmsConfig {
    @ApiModelProperty("阿里云 AccessKey ID")
    @JsonProperty("accessKeyId")
    private String accessKeyId;

    @ApiModelProperty("阿里云 AccessKey Secret")
    @JsonProperty("accessKeySecret")
    private String accessKeySecret;

    @ApiModelProperty("阿里云 Region，例如 cn-hangzhou")
    @JsonProperty("region")
    private String region;

    @ApiModelProperty("短信服务 Endpoint，例如 dysmsapi.aliyuncs.com")
    @JsonProperty("endpoint")
    private String endpoint;

    @ApiModelProperty("验证码有效时间（秒）")
    @JsonProperty("codeValidSecond")
    private Integer codeValidSecond;

    @ApiModelProperty("短信签名名称")
    @JsonProperty("signName")
    private String signName;
}
