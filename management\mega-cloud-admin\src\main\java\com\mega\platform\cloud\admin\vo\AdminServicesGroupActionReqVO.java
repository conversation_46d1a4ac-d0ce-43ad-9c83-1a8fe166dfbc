package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 服务组操作请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "服务组操作请求参数", description = "服务组操作请求参数")
public class AdminServicesGroupActionReqVO {
    
    /**
     * 服务组ID
     */
    @NotNull(message = "服务组ID不能为空")
    @ApiModelProperty(value = "服务组ID", required = true, example = "1")
    private Long servicesGroupId;
    
    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空")
    @ApiModelProperty(value = "操作类型", required = true, example = "1", notes = "NOTHING(0, \"nothing\", \"\"), RESTART(1, \"restart\", \"重启\"), STOP(2, \"stop\", \"停止\")")
    private Integer action;


    @ApiModelProperty(hidden = true)
    private Long adminUserId;
}
