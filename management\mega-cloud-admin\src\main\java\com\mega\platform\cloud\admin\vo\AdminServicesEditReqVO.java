package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
@ApiModel(value = "服务编辑请求参数")
public class AdminServicesEditReqVO {
    @ApiModelProperty(value = "微服务名称")
    private String name;

    @ApiModelProperty(value = "微服务组id")
    private Long serviceGroupId;

    @ApiModelProperty(value = "目标服务器id（ecs_server_id")
    private Long ecsServerId;

    @ApiModelProperty(value = "Jenkins 构建参数")
    private Map<String, String> jenkinsParams;

    @ApiModelProperty(value = "程序路径")
    private String path;

    @ApiModelProperty(value = "更新服务id")
    private Long servicesId;

    @ApiModelProperty(value = "日志路径")
    private String logPath;

    @ApiModelProperty(value = "日志状态")
    private Integer logStatus;

    @ApiModelProperty(value = "服务描述")
    private String description;

    @ApiModelProperty(value = "日志超时时间（秒）")
    private Integer logTimeoutSecond;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "备注信息")
    private String remark;
}
