package com.mega.platform.cloud.common.utils;

import com.mega.platform.cloud.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisUtils {

    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public RedisUtils(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    public void tryLock(String key, Callback callback) throws InterruptedException {
        Boolean flag;
        do {
            flag = stringRedisTemplate.opsForValue().setIfAbsent(key, new Date().toString());
            TimeUnit.SECONDS.sleep(1);
        } while (flag == null || !flag);
        try {
            callback.success();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new RuntimeException(e);
            }
        } finally {
            releaseLock(key);
        }
    }

    public Boolean tryLock(String key) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, new Date().toString());
    }

    public Boolean tryLockHash(String key) {
        return stringRedisTemplate.opsForHash().putIfAbsent(key, DateUtils.getTodayDate(),"1");
    }

    public Boolean tryLock(String key, long time, TimeUnit timeUnit) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, new Date().toString(), time, timeUnit);
    }

    public void releaseLock(String lockKey) {
        stringRedisTemplate.delete(lockKey);
    }

    public Boolean hasLocked(String key) {
        return stringRedisTemplate.opsForValue().get(key) != null;
    }

    @FunctionalInterface
    public interface Callback {
        void success() throws Exception;
    }
}
