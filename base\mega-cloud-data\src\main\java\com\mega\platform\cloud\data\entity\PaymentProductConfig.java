package com.mega.platform.cloud.data.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_product_config")
public class PaymentProductConfig {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 关联的支付平台ID
     */
    @Column(name = "third_platform_id")
    private Long thirdPlatformId;

    /**
     * App id
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 外部商品ID （比如苹果平台的sku）
     */
    @Column(name = "external_product_id")
    private String externalProductId;

    /**
     * 商品名称
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 是否为订阅产品
     */
    @Column(name = "is_subscription")
    private Boolean isSubscription;

    /**
     * 订阅周期（天）
     */
    @Column(name = "subscription_period_day")
    private Integer subscriptionPeriodDay;

    /**
     * 商品价格
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * 币种
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}