<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.mega.platform.cloud</groupId>
    <artifactId>mega-cloud</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.9</version>
    </parent>
    <modules>
        <module>third-party/mega-cloud-payment</module>
        <module>base/mega-cloud-common</module>
        <module>base/mega-cloud-core</module>
        <module>third-party/mega-cloud-push</module>
        <module>management/mega-cloud-admin</module>
        <module>third-party/mega-cloud-auth</module>
        <module>microservice/mega-cloud-microservice</module>
        <module>base/mega-cloud-generator</module>
        <module>management/mega-cloud-monitor</module>
        <module>management/mega-cloud-access</module>
        <module>base/mega-cloud-client</module>
        <module>base/mega-cloud-data</module>
        <module>base/mega-cloud-gateway</module>
    </modules>

    <properties>
        <revision>1.0.1</revision>
        <java.version>11</java.version>
        <git.branch>master</git.branch>
        <harbor.url>172.16.50.231:1180</harbor.url>
        <spring-boot-admin-starter-server.version>2.6.2</spring-boot-admin-starter-server.version>
        <spring-boot.version>2.6.3</spring-boot.version>
        <mysql-connector-java.version>5.1.49</mysql-connector-java.version>
        <mapper-spring-boot-starter.version>2.1.5</mapper-spring-boot-starter.version>
        <logbook-spring-boot-starter.version>2.9.0</logbook-spring-boot-starter.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <springfox-boot-starter.version>3.0.0</springfox-boot-starter.version>
        <mybatis-generator-core.version>1.4.0</mybatis-generator-core.version>
        <mapper.version>4.1.5</mapper.version>
        <freemarker.version>2.3.31</freemarker.version>
        <aliyun-sdk-oss.version>3.13.0</aliyun-sdk-oss.version>
        <server-sdk-java.version>3.2.12</server-sdk-java.version>
        <sentry-spring-boot-starter.version>3.1.1</sentry-spring-boot-starter.version>
        <dockerfile-maven-plugin.version>1.4.13</dockerfile-maven-plugin.version>
        <mybatis-generator-maven-plugin.version>1.3.7</mybatis-generator-maven-plugin.version>
        <alibaba-dingtalk-service-sdk.version>1.0.1</alibaba-dingtalk-service-sdk.version>
        <poi.version>4.1.2</poi.version>
        <poi-ooxml.version>4.1.2</poi-ooxml.version>
        <fastjson.version>1.2.83</fastjson.version>
        <ijapy.version>2.7.0</ijapy.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2020.0.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin-starter-server.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${mapper-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>logbook-spring-boot-starter</artifactId>
                <version>${logbook-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis-generator-core.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>${mapper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.rongcloud.im</groupId>
                <artifactId>server-sdk-java</artifactId>
                <version>${server-sdk-java.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-spring-boot-starter</artifactId>
                <version>${sentry-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-logback</artifactId>
                <version>${sentry-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${alibaba-dingtalk-service-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.22</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-cloudauth</artifactId>
                <version>2.0.29</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-saf</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>ru.yandex.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>0.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dypnsapi</artifactId>
                <version>1.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.70</version> <!-- 最新版本 -->
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Repository</name>
            <url>http://gitlab.53site.com:8081/repository/server-releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>repository.jboss.org-public</id>
            <name>JBoss.org Maven repository</name>
            <url>https://repository.jboss.org/nexus/content/groups/public</url>
        </repository>
    </repositories>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>default</id>
                            <phase>deploy</phase>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <skipDockerInfo>true</skipDockerInfo>
                        <repository>
                            ${harbor.url}/${project.parent.artifactId}/${project.artifactId}/${git.branch}
                        </repository>
                        <username>developer</username>
                        <password>LA1954b!</password>
                        <tag>latest</tag>
                        <buildArgs>
                            <JAR_FILE>${project.artifactId}-${project.version}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.mybatis.generator</groupId>
                    <artifactId>mybatis-generator-maven-plugin</artifactId>
                    <version>${mybatis-generator-maven-plugin.version}</version>
                    <configuration>
                        <verbose>true</verbose>
                        <overwrite>true</overwrite>
                        <configurationFile>
                            src/main/resources/generatorConfig.xml
                        </configurationFile>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>com.mega.platform.cloud</groupId>
                            <artifactId>mega-cloud-generator</artifactId>
                            <version>1.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.2.7</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
