<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminProjectDao">
        
    <!-- 基础查询条件 -->
    <sql id="BaseWhere">
        WHERE delsign = 0
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="status != null and status != -1">
            AND status = #{status}
        </if>
    </sql>
    
    <!-- 分页查询项目列表 -->
    <select id="selectProjectList" resultType="com.mega.platform.cloud.data.entity.Project">
        SELECT id, name, status, remark, create_time, update_time, delsign
        FROM project
        <include refid="BaseWhere"/>
        ORDER BY create_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>
    
    <!-- 查询项目总数 -->
    <select id="countProjectList" resultType="java.lang.Long">
        SELECT COUNT(id)
        FROM project
        <include refid="BaseWhere"/>
    </select>
    
    <!-- 根据ID查询项目 -->
    <select id="selectProjectById" resultType="com.mega.platform.cloud.data.entity.Project">
        SELECT id, name, status, remark, create_time, update_time, delsign
        FROM project
        WHERE id = #{id} AND delsign = 0
    </select>
    
    <!-- 根据名称查询项目 -->
    <select id="selectProjectByName" resultType="com.mega.platform.cloud.data.entity.Project">
        SELECT id, name, status, remark, create_time, update_time, delsign
        FROM project
        WHERE name = #{name} AND delsign = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>
    
    <!-- 逻辑删除项目 -->
    <update id="deleteProjectById">
        UPDATE project
        SET delsign = 1, update_time = NOW()
        WHERE id = #{id} AND delsign = 0
    </update>
    
</mapper>