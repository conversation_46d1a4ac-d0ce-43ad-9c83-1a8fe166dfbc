package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 权限管理-管理员列表查询请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员列表查询请求")
public class AdminAccessUserListReqVO {

    // @ApiModelProperty(value = "页码", example = "1")
    // private Integer pageNum = 1;

    // @ApiModelProperty(value = "每页大小", example = "20") 
    // private Integer pageSize = 20;

    @ApiModelProperty("用户名")
    private String adminUsername;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}