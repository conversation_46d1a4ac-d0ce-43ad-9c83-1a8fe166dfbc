package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 权限管理-角色创建请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色创建请求参数")
public class AdminAccessRoleCreateReqVO {

    @ApiModelProperty(value = "角色名称", example = "管理员")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String adminRoleName;

    @ApiModelProperty(value = "角色描述", example = "系统管理员角色")
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;

}