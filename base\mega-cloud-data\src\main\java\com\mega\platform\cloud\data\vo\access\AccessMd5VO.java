package com.mega.platform.cloud.data.vo.access;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
@ApiModel("文件MD5信息")
public class AccessMd5VO {
    @ApiModelProperty(value = "文件名", example = "config.xml", required = true)
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @ApiModelProperty(value = "文件MD5值", example = "d41d8cd98f00b204e9800998ecf8427e", required = true)
    @NotBlank(message = "MD5值不能为空")
    private String md5;
}
