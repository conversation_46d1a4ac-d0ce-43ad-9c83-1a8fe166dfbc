package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminAppPermissionDao;
import com.mega.platform.cloud.admin.vo.AdminAppPermissionListRespVO;
import com.mega.platform.cloud.admin.vo.AdminUrlPatternListRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * App访问平台权限服务类
 */
@Service
@Slf4j
public class AdminAppPermissionService {

    private final AdminAppPermissionDao adminAppPermissionDao;

    @Autowired
    public AdminAppPermissionService(AdminAppPermissionDao adminAppPermissionDao) {
        this.adminAppPermissionDao = adminAppPermissionDao;
    }

    /**
     * 获取路由配置列表
     * 查询project_url_pattern表中所有未删除的记录
     * 
     * @return 路由配置列表
     */
    public List<AdminUrlPatternListRespVO> urlPatternList() {
        log.info("查询路由配置列表");
        try {
            List<AdminUrlPatternListRespVO> result = adminAppPermissionDao.selectUrlPatternList();
            log.info("查询路由配置列表成功，共{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询路由配置列表失败", e);
            throw e;
        }
    }

    /**
     * 获取App权限列表
     * 查询project_app_permission表并关联project_app表获取App名称
     * 
     * @return App权限列表
     */
    public List<AdminAppPermissionListRespVO> appPermissionList() {
        log.info("查询App权限列表");
        try {
            List<AdminAppPermissionListRespVO> result = adminAppPermissionDao.selectAppPermissionList();
            log.info("查询App权限列表成功，共{}条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询App权限列表失败", e);
            throw e;
        }
    }
}
