package com.mega.platform.cloud.admin.service;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.dao.AdminAccessDao;
import com.mega.platform.cloud.admin.dto.AdminTokenPayload;
import com.mega.platform.cloud.admin.dto.AdminUserRoleBindingDTO;
import com.mega.platform.cloud.admin.util.AdminCryptUtils;
import com.mega.platform.cloud.admin.vo.AdminAccessRoleProjectBindingUpdateReqVO;
import com.mega.platform.cloud.admin.vo.AdminAccessRoleRouterBindingUpdateReqVO;
import com.mega.platform.cloud.admin.vo.AdminAccessRoleUserBindingUpdateReqVO;
import com.mega.platform.cloud.admin.vo.AdminAccessUserProjectBindingUpdateReqVO;
import com.mega.platform.cloud.admin.vo.AdminAccessUserRouterBindingUpdateReqVO;
import com.mega.platform.cloud.core.utils.JsonUtils;
import com.mega.platform.cloud.data.entity.AdminRouter;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import ru.yandex.clickhouse.Jackson;

@Service
@Slf4j
public class AdminAccessCacheService {
    private final StringRedisTemplate stringRedisTemplate;
    private final AdminAccessDao adminAccessDao;

    @Autowired
    public AdminAccessCacheService(StringRedisTemplate stringRedisTemplate, AdminAccessDao adminAccessDao) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.adminAccessDao = adminAccessDao;
    }

    /**
     * 刷新用户项目绑定的Redis缓存
     * 
     * @param req 用户项目绑定更新请求对象，包含:
     *           userId - 用户ID
     *           projectId - 项目ID
     *           delsign - 删除标记（0=增加绑定关系，1=删除绑定关系）
     */
    public void refreshUserProjectBinding(AdminAccessUserProjectBindingUpdateReqVO req) {
        String payloadKey = getPayloadKey(req.getUserId());
        String payloadJson = stringRedisTemplate.opsForValue().get(payloadKey);
        AdminTokenPayload payload = new AdminTokenPayload();

        try {
            if (StringUtils.hasText(payloadJson)) {
                payload = JsonUtils.fromJson(payloadJson, AdminTokenPayload.class);
            }

            // 初始化projectIds集合（如果为null）
            if (payload.getProjectIds() == null) {
                payload.setProjectIds(new HashSet<>());
            }

            // 根据delsign更新projectIds
            if (req.getDelsign() == 0) {
                payload.getProjectIds().add(req.getProjectId());
            } else {
                payload.getProjectIds().remove(req.getProjectId());
            }
            // 更新Redis中的payload
            stringRedisTemplate.opsForValue().set(payloadKey, JsonUtils.toJson(payload));
            log.info("刷新用户项目绑定缓存成功，用户ID: {}, 项目ID: {}, 操作: {}", req.getUserId(), req.getProjectId(),
                    req.getDelsign() == 0 ? "添加" : "删除");
        } catch (Exception e) {
            log.error("刷新用户项目绑定缓存失败", e);
        }
    }

    /**
     * 刷新用户路由绑定的Redis缓存
     * 
     * @param req 用户路由绑定更新请求对象，包含:
     *           userId - 用户ID
     *           routerId - 路由ID
     *           delsign - 删除标记（0=增加绑定关系，1=删除绑定关系）
     */
    public void refreshUserRouterBinding(AdminAccessUserRouterBindingUpdateReqVO req) {

        String payloadKey = getPayloadKey(req.getUserId());
        String payloadJson = stringRedisTemplate.opsForValue().get(payloadKey);
        AdminTokenPayload payload = new AdminTokenPayload();

        try {
            if (StringUtils.hasText(payloadJson)) {
                payload = JsonUtils.fromJson(payloadJson, AdminTokenPayload.class);
            }

            // 初始化backendPaths集合（如果为null）
            if (payload.getBackendPaths() == null) {
                payload.setBackendPaths(new HashSet<>());
            }

            // 查询路由信息获取backendPath
            AdminRouter router = adminAccessDao.selectRouterById(req.getRouterId());
            if (router != null && router.getBackendPath() != null) {
                // 根据delsign更新backendPaths
                if (req.getDelsign() == 0) {
                    payload.getBackendPaths().add(router.getBackendPath());
                } else {
                    payload.getBackendPaths().remove(router.getBackendPath());
                }

                // 更新Redis中的payload
                stringRedisTemplate.opsForValue().set(payloadKey, JsonUtils.toJson(payload));
                log.info("刷新用户路由绑定缓存成功，用户ID: {}, 路由ID: {}, 操作: {}", req.getUserId(), req.getRouterId(),
                        req.getDelsign() == 0 ? "添加" : "删除");
            }
        } catch (Exception e) {
            log.error("刷新用户路由绑定缓存失败", e);
        }
    }

    /**
     * 刷新角色路由绑定的Redis缓存
     * 
     * @param req 角色路由绑定更新请求对象，包含:
     *           adminRoleId - 角色ID
     *           adminRouterId - 路由ID
     *           delsign - 删除标记（0=增加绑定关系，1=删除绑定关系）
     */
    public void refreshRoleRouterBinding(AdminAccessRoleRouterBindingUpdateReqVO req) {
        try {
            // 查询该角色下的所有用户（排除超级管理员）
            List<AdminUserRoleBindingDTO> userRoleBindings = adminAccessDao
                    .selectRoleUserBindingList(req.getAdminRoleId(), null, 0);
            if (CollectionUtils.isEmpty(userRoleBindings)) {
                return;
            }
            // 路由数据
            AdminRouter router = adminAccessDao.selectRouterById(req.getAdminRouterId());
            if (router == null || router.getBackendPath() == null) {
                log.error("routerID {} is null", req.getAdminRouterId());
                return;
            }
            
            // 刷新用户路由绑定缓存
            for (AdminUserRoleBindingDTO binding : userRoleBindings) {
                String payloadKey = getPayloadKey(binding.getAdminUserId());
                String payloadJson = stringRedisTemplate.opsForValue().get(payloadKey);
                if(!StringUtils.hasText(payloadJson)) {
                    continue;
                }
                AdminTokenPayload payload = new AdminTokenPayload();
                payload = JsonUtils.fromJson(payloadJson, AdminTokenPayload.class);
                if (req.getDelsign() == 0) {
                    payload.getBackendPaths().add(router.getBackendPath());
                } else {
                    payload.getBackendPaths().remove(router.getBackendPath());
                }
               stringRedisTemplate.opsForValue().set(payloadKey, JsonUtils.toJson(payload));
               log.info("刷新用户路由绑定缓存成功，用户ID: {}, 路由ID: {}, 操作: {}", binding.getAdminUserId(), req.getAdminRouterId(),
                        req.getDelsign() == 0 ? "添加" : "删除");
            }

            log.info("刷新角色路由绑定缓存成功，角色ID: {}, 路由ID: {}, 操作: {}", req.getAdminRoleId(), req.getAdminRouterId(), req.getDelsign() == 0 ? "添加" : "删除");
        } catch (Exception e) {
            log.error("刷新角色路由绑定缓存失败", e);

        }
    }

     /**
      * 刷新角色项目绑定的Redis缓存
      * 
      * @param req 角色项目绑定更新请求对象，包含:
      *           adminRoleId - 角色ID
      *           projectId - 项目ID  
      *           delsign - 删除标记（0=增加绑定关系，1=删除绑定关系）
      */
    public void refreshRoleProjectBinding(AdminAccessRoleProjectBindingUpdateReqVO req) {
        if (req.getAdminRoleId() == null || req.getProjectId() == null || req.getDelsign() == null) {
            throw new AdminException(4000, "参数不能为空");
        }

        try {
            // 查询该角色下的所有用户（排除超级管理员）
            List<AdminUserRoleBindingDTO> userRoleBindings = adminAccessDao.selectRoleUserBindingList(req.getAdminRoleId(), null, 0);
            if (CollectionUtils.isEmpty(userRoleBindings)) {
                return;
            }

            // 刷新用户项目绑定缓存
            for (AdminUserRoleBindingDTO binding : userRoleBindings) {
                String payloadKey = getPayloadKey(binding.getAdminUserId());
                String payloadJson = stringRedisTemplate.opsForValue().get(payloadKey);
                if(!StringUtils.hasText(payloadJson)) {
                    continue;
                }
                AdminTokenPayload payload = new AdminTokenPayload();
                payload = JsonUtils.fromJson(payloadJson, AdminTokenPayload.class);
                
                // 初始化projectIds集合（如果为null）
                if (payload.getProjectIds() == null) {
                    payload.setProjectIds(new HashSet<>());
                }
                
                if (req.getDelsign() == 0) {
                    payload.getProjectIds().add(req.getProjectId());
                } else {
                    payload.getProjectIds().remove(req.getProjectId());
                }
                stringRedisTemplate.opsForValue().set(payloadKey, JsonUtils.toJson(payload));
                log.info("刷新用户项目绑定缓存成功，用户ID: {}, 项目ID: {}, 操作: {}", binding.getAdminUserId(), req.getProjectId(),
                        req.getDelsign() == 0 ? "添加" : "删除");
            }
            log.info("刷新角色项目绑定缓存成功，角色ID: {}, 项目ID: {}, 操作: {}", req.getAdminRoleId(), req.getProjectId(), req.getDelsign() == 0 ? "添加" : "删除");
        } catch (Exception e) {
            log.error("刷新角色项目绑定缓存失败", e);
        }
    }

    /**
     * 刷新角色用户绑定的Redis缓存
     * 
     * @param req 角色用户绑定更新请求对象，包含:
     *           adminRoleId - 角色ID
     *           adminUserId - 用户ID
     *           delsign - 删除标记（0=增加绑定关系，1=删除绑定关系）
     */
    public void refreshRoleUserBinding(AdminAccessRoleUserBindingUpdateReqVO req) {
        String payloadKey = getPayloadKey(req.getAdminUserId());
        String payloadJson = stringRedisTemplate.opsForValue().get(payloadKey);
        AdminTokenPayload payload = new AdminTokenPayload();

        try {
            if (StringUtils.hasText(payloadJson)) {
                payload = JsonUtils.fromJson(payloadJson, AdminTokenPayload.class);
            }

            // 初始化roleIds集合（如果为null）
            if (payload.getRoleIds() == null) {
                payload.setRoleIds(new HashSet<>());
            }

            // 根据delsign更新roleIds
            if (req.getDelsign() == 0) {
                payload.getRoleIds().add(req.getAdminRoleId());
            } else {
                payload.getRoleIds().remove(req.getAdminRoleId());
            }

            // 更新Redis中的payload
            stringRedisTemplate.opsForValue().set(payloadKey, JsonUtils.toJson(payload));
            log.info("刷新角色用户绑定缓存成功，角色ID: {}, 用户ID: {}, 操作: {}", req.getAdminRoleId(), req.getAdminUserId(), req.getDelsign() == 0 ? "添加" : "删除");
        } catch (Exception e) {
            log.error("刷新角色用户绑定缓存失败", e);
        }
    }

    /**
     * 获取payloadKey
     * 
     * @param adminUserId
     * @return
     */
    public String getPayloadKey(Long adminUserId) {
        return AdminAuthConstant.REDIS_USER_PAYLOAD_PREFIX + adminUserId;
    }

    /**
     * 刷新token
     * 
     * @param adminUserId
     * @param roleIds
     * @param projectIds
     * @param permissions
     * @return
     */
    public String refreshToken(Long adminUserId, Set<Long> roleIds, Set<Long> projectIds, Set<String> permissions) {

        String payloadKey = getPayloadKey(adminUserId);

        AdminTokenPayload oldPayload = new AdminTokenPayload();
        String oldPayloadJson = stringRedisTemplate.opsForValue().get(payloadKey);
        String token = null;

        try {
            if (StringUtils.hasText(oldPayloadJson)) {
                // 将Redis中的旧payload转换为AdminTokenPayload对象
                oldPayload = JsonUtils.fromJson(oldPayloadJson, AdminTokenPayload.class);
            } else {
                // 设置版本为当前时间戳
                oldPayload.setTokenVersion(System.currentTimeMillis() / 1000);
            }
            // 6. 生成简化的JWT Token（只包含adminUserId和tokenVersion）
            token = AdminCryptUtils.generateSimpleJwtToken(adminUserId, oldPayload.getTokenVersion());

            // 7. 创建AdminTokenPayload对象并缓存到Redis（合并多次写入为一次）
            oldPayload.setProjectIds(projectIds);
            oldPayload.setBackendPaths(permissions);
            oldPayload.setRoleIds(roleIds);
            String payloadValue = JsonUtils.toJson(oldPayload);
            stringRedisTemplate.opsForValue().set(payloadKey, payloadValue, AdminAuthConstant.JWT_EXPIRATION_TIME,
                    TimeUnit.SECONDS);

            return token;
        } catch (Exception exception) {
            log.error("登录异常", exception);
            throw new AdminException(5000, "登录异常");
        }
    }

    /**
     * 删除token
     * 
     * @param adminUserId
     */
    public void deleteToken(Long adminUserId) {
        // 1. 生成新的token版本，使所有旧token失效
        String payloadKey = AdminAuthConstant.REDIS_USER_PAYLOAD_PREFIX + adminUserId;
        stringRedisTemplate.delete(payloadKey);
        log.info("管理员全部登出成功，用户ID: {}", adminUserId);
    }

    /**
     * 验证JWT Token
     *
     * @param token JWT Token
     * @return 管理员用户ID，验证失败返回null
     */
    public AdminTokenPayload validateToken(String token) {
        try {
            Claims claims = AdminCryptUtils.parseJwtToken(token);
            Long adminUserId = claims.get(AdminAuthConstant.CONTEXT_ADMIN_USER_ID, Long.class);
            Long tokenVersion = claims.get(AdminAuthConstant.CONTEXT_TOKEN_VERSION, Long.class);

            // 验证token版本
            String payloadKey = getPayloadKey(adminUserId);
            String oldPayloadValue = stringRedisTemplate.opsForValue().get(payloadKey);
            AdminTokenPayload payload = new AdminTokenPayload();
            if (StringUtils.hasText(oldPayloadValue)) {
                // 将Redis中的旧payload转换为AdminTokenPayload对象
                payload = JsonUtils.fromJson(oldPayloadValue, AdminTokenPayload.class);
            }
            payload.setAdminUserId(adminUserId);

            if (payload.getTokenVersion() == null || !tokenVersion.equals(payload.getTokenVersion())) {
                log.warn("Token版本验证失败，用户ID: {}, Token版本: {}, 缓存版本: {}",
                        adminUserId, tokenVersion, payload.getTokenVersion());
                return null;
            }

            return payload;
        } catch (Exception e) {
            log.warn("Token验证失败: {}", e.getMessage());
            return null;
        }
    }
}
